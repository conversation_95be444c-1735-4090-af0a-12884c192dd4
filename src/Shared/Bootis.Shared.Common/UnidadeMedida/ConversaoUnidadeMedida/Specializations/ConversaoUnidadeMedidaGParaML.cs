using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida.Specializations;

public class ConversaoUnidadeMedidaGParaML : ConversaoUnidadeMedida
{
    public override UnidadeMedidaAbreviacao UnidadeMedidaOrigem => UnidadeMedidaAbreviacao.g;
    public override UnidadeMedidaAbreviacao UnidadeMedidaConversao => UnidadeMedidaAbreviacao.mL;
    public override decimal TaxaConversao => 1;
    public override TipoCalculoDensidade TipoCalculoDensidade => TipoCalculoDensidade.Divide;
}