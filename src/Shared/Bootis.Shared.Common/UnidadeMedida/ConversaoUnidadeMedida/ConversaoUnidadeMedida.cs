using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida;

public abstract class ConversaoUnidadeMedida
{
    public abstract UnidadeMedidaAbreviacao UnidadeMedidaOrigem { get; }
    public abstract UnidadeMedidaAbreviacao UnidadeMedidaConversao { get; }
    public abstract decimal TaxaConversao { get; }
    public abstract TipoCalculoDensidade TipoCalculoDensidade { get; }

    public decimal CalcularConversao(decimal quantidade, decimal? densidade = null)
    {
        switch (TipoCalculoDensidade)
        {
            case TipoCalculoDensidade.Divide:
                if (densidade.HasValue)
                    return quantidade * TaxaConversao / densidade.Value;
                return quantidade * TaxaConversao;
            case TipoCalculoDensidade.Multiplica:
                if (densidade.HasValue)
                    return quantidade * TaxaConversao * densidade.Value;
                return quantidade * TaxaConversao;
            case TipoCalculoDensidade.Nenhum:
                return quantidade * TaxaConversao;
            default:
                return 0;
        }
    }

    public decimal CalcularConversaoMonetario(decimal quantidade)
    {
        return quantidade / TaxaConversao;
    }
}