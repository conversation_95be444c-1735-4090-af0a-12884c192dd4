using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida;

public static class ConversaoUnidadeMedidaCreator
{
    private static readonly
        Dictionary<(UnidadeMedidaAbreviacao origem, UnidadeMedidaAbreviacao destino), ConversaoUnidadeMedida>
        ConversaoUnidadeMedida = new();

    private static readonly object Lock = new();

    public static ConversaoUnidadeMedida Criar(UnidadeMedidaAbreviacao unidadeMedidaOrigem,
        UnidadeMedidaAbreviacao unidadeMedidaDestino)
    {
        lock (Lock)
        {
            if (ConversaoUnidadeMedida.Count == 0) InitializeDictionary();

            if (ConversaoUnidadeMedida.TryGetValue((unidadeMedidaOrigem, unidadeMedidaDestino), out var resultado))
                return resultado;

            return null;
        }
    }

    private static void InitializeDictionary()
    {
        var allTypes =
            from x in typeof(ConversaoUnidadeMedida).Assembly.GetTypes()
            let y = x.BaseType
            where !x.IsAbstract && !x.IsInterface &&
                  y == typeof(ConversaoUnidadeMedida)
            select x;

        foreach (var type in allTypes)
        {
            var instance = (ConversaoUnidadeMedida)Activator.CreateInstance(type);

            ConversaoUnidadeMedida.Add((instance.UnidadeMedidaOrigem, instance.UnidadeMedidaConversao), instance);
        }
    }
}