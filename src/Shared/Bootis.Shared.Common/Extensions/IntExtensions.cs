namespace Bootis.Shared.Common.Extensions;

public static class IntExtensions
{
    public static Guid ToGuid(this int value)
    {
        var bytes = new byte[16];

        BitConverter.GetBytes(value).CopyTo(bytes, 0);

        return new Guid(bytes);
    }

    public static Guid ToGuid(this Enum value)
    {
        return Convert.ToInt32(value).ToGuid();
    }

    public static int ToInt(this Enum value)
    {
        return Convert.ToInt32(value);
    }
}