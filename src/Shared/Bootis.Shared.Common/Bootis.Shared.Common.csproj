<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{2c43adf8-6c09-40aa-b86a-697add21303b}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FluentValidation" Version="12.0.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0"/>
        <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.8" />
        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.14.0" />
        <PackageReference Include="OneOf" Version="3.0.271"/>
        <PackageReference Include="Serilog" Version="4.3.0"/>
        <PackageReference Include="UUIDNext" Version="4.2.0"/>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Bootis.Shared\Bootis.Shared.csproj"/>
    </ItemGroup>

</Project>

