<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ConfirmEmail_BtnTitle" xml:space="preserve">
    <value>Confirm email</value>
  </data>
  <data name="ConfirmEmai_Help" xml:space="preserve">
    <value>If you have any questions, please contact our support via email:</value>
  </data>
  <data name="ConfirmEmai_LinkDescription" xml:space="preserve">
    <value>Or access the link below:</value>
  </data>
  <data name="ConfirmEmai_RecoverDescriptionEnd" xml:space="preserve">
    <value>is yours, your email address will be updated and you'll be able to continue accessing the platform normally.</value>
  </data>
  <data name="ConfirmEmai_RecoverDescriptionInitial" xml:space="preserve">
    <value>After you confirm that the email address</value>
  </data>
  <data name="ConfirmEmai_Welcome" xml:space="preserve">
    <value>Email confirmation</value>
  </data>
  <data name="ConfirmEmai_Subject" xml:space="preserve">
    <value>{0} Email confirmation</value>
  </data>
  <data name="NewPassword_BtnTitle" xml:space="preserve">
    <value>Confirm email</value>
  </data>
  <data name="NewPassword_Help" xml:space="preserve">
    <value>In case of doubt, please contact our support by e-mail:</value>
  </data>
  <data name="NewPassword_LinkDescription" xml:space="preserve">
    <value>Or access the link below:</value>
  </data>
  <data name="NewPassword_RecoverDescriptionEnd" xml:space="preserve">
    <value>It's yours, you will be directed to the definition of your access password.</value>
  </data>
  <data name="NewPassword_RecoverDescriptionInitial" xml:space="preserve">
    <value>After your confirmation that the email address</value>
  </data>
  <data name="NewPassword_Welcome" xml:space="preserve">
    <value>Welcome to Boötis!</value>
  </data>
  <data name="NewPassword_Subject" xml:space="preserve">
    <value>{0} Welcome to Boötis!</value>
  </data>
  <data name="ResetPassword_BtnTitle" xml:space="preserve">
    <value>Recover Password</value>
  </data>
  <data name="ResetPassword_Disregard" xml:space="preserve">
    <value>If you have not requested a password change, please disregard this email.</value>
  </data>
  <data name="ResetPassword_Help" xml:space="preserve">
    <value>In case of doubt, please contact our support by e-mail:</value>
  </data>
  <data name="ResetPassword_LinkDescription" xml:space="preserve">
    <value>Or access the link below</value>
  </data>
  <data name="ResetPassword_RecoverDescription" xml:space="preserve">
    <value>To reset your password, click on the button below</value>
  </data>
  <data name="ResetPassword_Required" xml:space="preserve">
    <value>A password recovery was requested for the email:</value>
  </data>
  <data name="ResetPassword_Subject" xml:space="preserve">
    <value>Recover Password</value>
  </data>
  <data name="EmailTemplate_NotFound" xml:space="preserve">
    <value>The email template {0} was not found</value>
  </data>
  <data name="Email_ConfirmeEmail" xml:space="preserve">
    <value>Confirm Email</value>
  </data>
  <data name="Email_CriarNovaSenha" xml:space="preserve">
    <value>Create New Password</value>
  </data>
  <data name="Email_NovaSenha" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="Email_ResetarSenha" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="Email_Validation" xml:space="preserve">
    <value>Email is already being used</value>
  </data>
  <data name="Login_UsuarioSenhaInvalido" xml:space="preserve">
    <value>Username or password is invalid.</value>
  </data>
  <data name="RefreshToken_TokenInvalido" xml:space="preserve">
    <value>Token not found or not expired.</value>
  </data>
  <data name="Usuario_CodigoInvalido" xml:space="preserve">
    <value>Invalid code.</value>
  </data>
  <data name="Usuario_NaoEncontrado" xml:space="preserve">
    <value>User not found.</value>
  </data>
  <data name="Usuario_SenhaInvalida" xml:space="preserve">
    <value>Invalid password.</value>
  </data>
  <data name="Conglomerado_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any cluster with Guid: {0}</value>
  </data>
  <data name="Usuario_EmailJaCadastrado" xml:space="preserve">
    <value>A user with this email already exists.</value>
  </data>
  <data name="EmpresaMatriz_IdNaoEncontrado" xml:space="preserve">
    <value>It was not possible to find any parent company for the conglomerate with the Id: {0}</value>
  </data>
  <data name="EmpresaPagadora_Guid_NaoEncontrado" xml:space="preserve">
    <value>It was not possible to find any responsible company for payment with the guid: {0}</value>
  </data>
  <data name="Empresa_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any company with guid: {0}</value>
  </data>
  <data name="Empresa_NaoEncontrada" xml:space="preserve">
    <value>Could not find any company</value>
  </data>
  <data name="Grupo_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Group with the guid: {0}</value>
  </data>
  <data name="Grupo_NomeJaCadastrado" xml:space="preserve">
    <value>A Group with this name already exists.</value>
  </data>
  <data name="Usuario_Responsavel" xml:space="preserve">
    <value>User responsible for a company, cannot be exclusive.</value>
  </data>
  <data name="Usuario_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any User with the guid: {0}</value>
  </data>
  <data name="Usuario_NenhumLogadoEncontrado" xml:space="preserve">
    <value>No logged in user found.</value>
  </data>
  <data name="Usuario_Requerido" xml:space="preserve">
    <value>Required User.</value>
  </data>
  <data name="Validation_CampoRequerido" xml:space="preserve">
    <value>The {0} field is required.</value>
  </data>
  <data name="Cnpj_JaCadastrado" xml:space="preserve">
    <value>CNPJ already registered.</value>
  </data>
  <data name="Permissao_Administrativo" xml:space="preserve">
    <value>Administrative</value>
  </data>
  <data name="Permissao_AdministrativoEmpresas" xml:space="preserve">
    <value>Companies</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuario" xml:space="preserve">
    <value>User Groups</value>
  </data>
  <data name="Permissao_AdministrativoUsuario" xml:space="preserve">
    <value>Usuários</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioAlterarStatus" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioEditarDadosPessoais" xml:space="preserve">
    <value>Edit users personal data</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioEditarGrupo" xml:space="preserve">
    <value>Edit user groups</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioEditarPermissao" xml:space="preserve">
    <value>Edit user permissions</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioExcluir" xml:space="preserve">
    <value>Delete users</value>
  </data>
  <data name="Permissao_AdministrativoUsuariosCadastrar" xml:space="preserve">
    <value>Register new users</value>
  </data>
  <data name="Permissao_AdministrativoUsuariosVerDetalhes" xml:space="preserve">
    <value>View user details</value>
  </data>
  <data name="Permissao_AdministrativoUsuarioVerLista" xml:space="preserve">
    <value>View user list</value>
  </data>
  <data name="Permissao_Compras" xml:space="preserve">
    <value>Shopping</value>
  </data>
  <data name="Permissao_ComprasFornecedores" xml:space="preserve">
    <value>Providers</value>
  </data>
  <data name="Permissao_Estoque" xml:space="preserve">
    <value>Inventory</value>
  </data>
  <data name="Permissao_EstoqueAjusteSaldo" xml:space="preserve">
    <value>Balance Adjustment</value>
  </data>
  <data name="Permissao_EstoqueLocaisLotes" xml:space="preserve">
    <value>Stock Locations</value>
  </data>
  <data name="Permissao_EstoqueLotes" xml:space="preserve">
    <value>Lots</value>
  </data>
  <data name="Permissao_EstoqueRastreabilidadeVisualizar" xml:space="preserve">
    <value>View batch traceability</value>
  </data>
  <data name="EstoqueRastreabilidade" xml:space="preserve">
    <value>Traceability</value>
  </data>
  <data name="Permissao_EstoqueMovimentacaoVisualizar" xml:space="preserve">
    <value>View stock movement</value>
  </data>
  <data name="EstoqueMovimentacao" xml:space="preserve">
    <value>Stock movement</value>
  </data>
  <data name="Permissao_EstoquePerdas" xml:space="preserve">
    <value>Losses</value>
  </data>
  <data name="Permissao_EstoqueTransferencia" xml:space="preserve">
    <value>Transfers</value>
  </data>
  <data name="Permissao_EstoqueGrupoProdutos" xml:space="preserve">
    <value>Product Groups</value>
  </data>
  <data name="Permissao_EstoqueProdutos" xml:space="preserve">
    <value>Produtos</value>
  </data>
  <data name="Permissao_EstoqueSubgruposProdutos" xml:space="preserve">
    <value>Products</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaEditarDados" xml:space="preserve">
    <value>Edit company data</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaEditarEndereco" xml:space="preserve">
    <value>Edit company address</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaEditarStatus" xml:space="preserve">
    <value>Change company status</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaEditarUsuarioResponsavel" xml:space="preserve">
    <value>Edit company responsible user</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaVerDetalhes" xml:space="preserve">
    <value>View company details</value>
  </data>
  <data name="Permissao_AdministrativoEmpresaVerLista" xml:space="preserve">
    <value>See list of companies</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosAlterarStatus" xml:space="preserve">
    <value>Change status of user groups</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosCriar" xml:space="preserve">
    <value>Create user groups</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosEditarDetalhes" xml:space="preserve">
    <value>Edit user group details</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosEditarPermissao" xml:space="preserve">
    <value>Edit user group permissions</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosExcluir" xml:space="preserve">
    <value>Delete user groups</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosVerDetalhes" xml:space="preserve">
    <value>View user group details</value>
  </data>
  <data name="Permissao_AdministrativoGrupoUsuariosVerLista" xml:space="preserve">
    <value>View list of user groups</value>
  </data>
  <data name="Permissao_ComprasFornecedoresAlterarStatus" xml:space="preserve">
    <value>Change supplier status</value>
  </data>
  <data name="Permissao_ComprasFornecedoresCadastrar" xml:space="preserve">
    <value>Register new suppliers</value>
  </data>
  <data name="Permissao_ComprasFornecedoresEditarContatos" xml:space="preserve">
    <value>Edit supplier contacts</value>
  </data>
  <data name="Permissao_ComprasFornecedoresEditarDetalhes" xml:space="preserve">
    <value>Edit supplier details</value>
  </data>
  <data name="Permissao_ComprasFornecedoresEditarDocumentos" xml:space="preserve">
    <value>Edit supplier documents</value>
  </data>
  <data name="Permissao_ComprasFornecedoresEditarEnderecos" xml:space="preserve">
    <value>Edit vendor addresses</value>
  </data>
  <data name="Permissao_ComprasFornecedoresExcluir" xml:space="preserve">
    <value>Exclude suppliers</value>
  </data>
  <data name="Permissao_ComprasFornecedoresVerDetalhes" xml:space="preserve">
    <value>View supplier details</value>
  </data>
  <data name="Permissao_ComprasFornecedoresVerLista" xml:space="preserve">
    <value>See list of suppliers</value>
  </data>
  <data name="Permissao_ComprasNecessidadeComprasVerLista" xml:space="preserve">
    <value>View shopping list</value>
  </data>
  <data name="Permissao_EstoqueAjusteSaldoCadastrar" xml:space="preserve">
    <value>Register new balance adjustments</value>
  </data>
  <data name="Permissao_EstoqueAjusteSaldoVerDetalhes" xml:space="preserve">
    <value>View balance adjustment details</value>
  </data>
  <data name="Permissao_EstoqueAjusteSaldoVerLista" xml:space="preserve">
    <value>View balance adjustment list</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueAlterarStatus" xml:space="preserve">
    <value>Change status of stock locations</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueCadastrar" xml:space="preserve">
    <value>Register new stock locations</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueEditarDetalhes" xml:space="preserve">
    <value>Edit stock locations details</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueExcluir" xml:space="preserve">
    <value>Exclude stock locations</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueVerDetalhes" xml:space="preserve">
    <value>View details of stock locations</value>
  </data>
  <data name="Permissao_EstoqueLocaisEstoqueVerLista" xml:space="preserve">
    <value>View list of stock locations</value>
  </data>
  <data name="Permissao_EstoqueLotesAlterarStatus" xml:space="preserve">
    <value>Change batch status</value>
  </data>
  <data name="Permissao_EstoqueLotesCadastrar" xml:space="preserve">
    <value>Register new batches</value>
  </data>
  <data name="Permissao_EstoqueLotesEditarInformacoes" xml:space="preserve">
    <value>Edit batch information</value>
  </data>
  <data name="Permissao_EstoqueLotesExcluir" xml:space="preserve">
    <value>Delete batches</value>
  </data>
  <data name="Permissao_EstoqueLotesVerDetalhes" xml:space="preserve">
    <value>View batch details</value>
  </data>
  <data name="Permissao_EstoqueLotesVerLista" xml:space="preserve">
    <value>See list of companies</value>
  </data>
  <data name="Permissao_EstoquePerdasCadastrar" xml:space="preserve">
    <value>Register new losses</value>
  </data>
  <data name="Permissao_EstoquePerdasEditarDetalhes" xml:space="preserve">
    <value>Edit loss details</value>
  </data>
  <data name="Permissao_EstoquePerdasVerDetalhes" xml:space="preserve">
    <value>View loss details</value>
  </data>
  <data name="Permissao_EstoquePerdasVerLista" xml:space="preserve">
    <value>See list of losses</value>
  </data>
  <data name="Permissao_EstoqueTransferenciasCadastrar" xml:space="preserve">
    <value>Register new transfers</value>
  </data>
  <data name="Permissao_EstoqueTransferenciasVerDetalhes" xml:space="preserve">
    <value>View transfer details</value>
  </data>
  <data name="Permissao_EstoqueTransferenciasVerLista" xml:space="preserve">
    <value>View transfer list</value>
  </data>
  <data name="Permissao_EstoqueGruposCadastrar" xml:space="preserve">
    <value>Register new product groups</value>
  </data>
  <data name="Permissao_EstoqueGruposEditarDetalhes" xml:space="preserve">
    <value>Edit product group details</value>
  </data>
  <data name="Permissao_EstoqueGruposExcluir" xml:space="preserve">
    <value>Delete product groups</value>
  </data>
  <data name="Permissao_EstoqueGruposVerDetalhes" xml:space="preserve">
    <value>View product group details</value>
  </data>
  <data name="Permissao_EstoqueGruposVerLista" xml:space="preserve">
    <value>View list of product groups</value>
  </data>
  <data name="Permissao_EstoqueProdutosAlterarStatus" xml:space="preserve">
    <value>Change product status</value>
  </data>
  <data name="Permissao_EstoqueProdutosCadastrar" xml:space="preserve">
    <value>Register new products</value>
  </data>
  <data name="Permissao_EstoqueProdutosEditarInformacoes" xml:space="preserve">
    <value>Edit product information</value>
  </data>
  <data name="Permissao_EstoqueProdutosEditarInformacoesFinanceiras" xml:space="preserve">
    <value>Edit product financial information</value>
  </data>
  <data name="Permissao_EstoqueProdutosExcluir" xml:space="preserve">
    <value>Delete products</value>
  </data>
  <data name="Permissao_EstoqueProdutosVerDetalhes" xml:space="preserve">
    <value>View product details</value>
  </data>
  <data name="Permissao_EstoqueProdutosVerLista" xml:space="preserve">
    <value>See product list</value>
  </data>
  <data name="Permissao_EstoqueSubGruposCadastrar" xml:space="preserve">
    <value>Register new product subgroups</value>
  </data>
  <data name="Permissao_EstoqueSubGruposEditarDetalhes" xml:space="preserve">
    <value>Edit product subgroup details</value>
  </data>
  <data name="Permissao_EstoqueSubGruposExcluir" xml:space="preserve">
    <value>Exclude product subgroups</value>
  </data>
  <data name="Permissao_EstoqueSubGruposVerDetalhes" xml:space="preserve">
    <value>View product subgroup details</value>
  </data>
  <data name="Permissao_EstoqueSubGruposVerLista" xml:space="preserve">
    <value>View list of product subgroups</value>
  </data>
  <data name="Permissao_ErroAoAtualizar" xml:space="preserve">
    <value>Could not update permission, review dependencies.</value>
  </data>
  <data name="AjusteSaldoEstoque_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any stock ajust balance with guid: {0}</value>
  </data>
  <data name="ClassificacaoProduto_GuidNaoEncontrado" xml:space="preserve">
    <value>No Product Classification found with guid: {0}</value>
  </data>
  <data name="ConversaoUnidadeMedida_IdNaoEncontrado" xml:space="preserve">
    <value>Unable to convert Unit of Measure Id {0} with source Unit of Measure Id {1}.</value>
  </data>
  <data name="ConversaUnidadeMedida_UnidadesJaCadastradas" xml:space="preserve">
    <value>There is already a unit of measure conversion registered with the units informed: Source: {0} Conversion: {1}</value>
  </data>
  <data name="Fornecedor_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find a supplier with guid: {0}</value>
  </data>
  <data name="Grupo_DescricaoExistente" xml:space="preserve">
    <value>There is already a Group with the description {0}</value>
  </data>
  <data name="Grupo_ExclusaoProibida" xml:space="preserve">
    <value>The Group cannot be deleted because it is linked to {0} product(s) e {1} subgroup(s).</value>
  </data>
  <data name="LancamentoPerda_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Loss with Id: {0}</value>
  </data>
  <data name="LancamentoPerda_SaldoEstoqueInsuficiente" xml:space="preserve">
    <value>Insufficient inventory balance to carry out the loss: {0}</value>
  </data>
  <data name="LocalEstoque_DescricaoExistente" xml:space="preserve">
    <value>An Stock Location {0} already exists for company with Id {1}.</value>
  </data>
  <data name="LocalEstoque_EmUso" xml:space="preserve">
    <value>The Stock Location {0} cannot be deleted as it is listed in other parts of the system.</value>
  </data>
  <data name="LocalEstoque_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Stock Location with guid: {0}</value>
  </data>
  <data name="Lote_DataValidadeVencida" xml:space="preserve">
    <value>It is not possible to register an expired Batch.</value>
  </data>
  <data name="Lote_DensidadeInvalida" xml:space="preserve">
    <value>Density of {0} reported for Lot {1} is invalid.</value>
  </data>
  <data name="Lote_EmUso" xml:space="preserve">
    <value>Batch {0} has already been moved within the system and therefore cannot be deleted.</value>
  </data>
  <data name="Lote_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Lot with guid: {0}</value>
  </data>
  <data name="Lote_NaoPossuiQuantidadeDeProduto" xml:space="preserve">
    <value>Batch {0} does not have the quantity of product {1}</value>
  </data>
  <data name="Lote_NumeroJaExistente" xml:space="preserve">
    <value>Batch Number {0} already exists for the product.</value>
  </data>
  <data name="Lote_QuantidadeInvalida" xml:space="preserve">
    <value>Quantity entered for the invalid Batch.</value>
  </data>
  <data name="MotivoPerda_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any reason for loss with guid: {0}</value>
  </data>
  <data name="MovimentoEstoque_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Stock Movement with guid: {0}</value>
  </data>
  <data name="OperacaoEstoque_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any stock operation with guid: {0}</value>
  </data>
  <data name="Pais_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Country with guid: {0}</value>
  </data>
  <data name="Produto_DescricaoExistente" xml:space="preserve">
    <value>There is already another Product with the description {0}</value>
  </data>
  <data name="Produto_GuidNaoEncontrado" xml:space="preserve">
    <value>No Product found with guid {0}</value>
  </data>
  <data name="Produto_GuidNaoPodeSerExcluido" xml:space="preserve">
    <value>The Product guid: {0} cannot be deleted as it is related to other parts of the system.</value>
  </data>
  <data name="Produto_PossuiQuantidadeEmEstoque" xml:space="preserve">
    <value>Product {0} has quantity in stock and therefore it is not possible to disable batch control.</value>
  </data>
  <data name="SaldoEstoque_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Inventory Balance with Lot guid: {0}</value>
  </data>
  <data name="SaldoEstoque_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any Inventory Balance with Lot id: {0} and Local Stock id: {1}</value>
  </data>
  <data name="SituacaoLote_Ativo" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="SituacaoLote_Bloqueado" xml:space="preserve">
    <value>Blocked</value>
  </data>
  <data name="SituacaoLote_ControleQualidade" xml:space="preserve">
    <value>Quality control</value>
  </data>
  <data name="SituacaoLote_Inativo" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="SubGrupo_DescricaoExistente" xml:space="preserve">
    <value>There is already a Subgroup with the description {0} for the Group.</value>
  </data>
  <data name="SubGrupo_ExclusaoProibida" xml:space="preserve">
    <value>The Subgroup cannot be deleted because it is linked to {0} product(s).</value>
  </data>
  <data name="SubGrupo_IdNaoLocalizado" xml:space="preserve">
    <value>No Subgroups found with guid {0}</value>
  </data>
  <data name="SubGrupo_NaoPertenceAoGrupoPrincipal" xml:space="preserve">
    <value>Subgroup of guid: {0}, does not belong to group of guid: {1}</value>
  </data>
  <data name="SubGrupo_NaoPodeSerCadastradoComoGrupoPai" xml:space="preserve">
    <value>It is not possible to register a SubGroup as a ParentGroup</value>
  </data>
  <data name="UnidadeMedida_AbreviacaoExistente" xml:space="preserve">
    <value>Already registered abbreviation {0}.</value>
  </data>
  <data name="UnidadeMedida_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Unit of Measure with guid: {0}</value>
  </data>
  <data name="Usuario_IdInvalido" xml:space="preserve">
    <value>User informed, id: {0} not found</value>
  </data>
  <data name="Validation_CampoInvalido" xml:space="preserve">
    <value>The {0} field has an invalid value.</value>
  </data>
  <data name="Validation_TamanhoMaximo" xml:space="preserve">
    <value>Field {0} has maximum length {1}.</value>
  </data>
  <data name="TipoCapsula_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any capsule type with id: {0}</value>
  </data>
  <data name="ProdutoSinonimo_GuidNaoEncontrado" xml:space="preserve">
    <value>No synonymous products found with guid {0}</value>
  </data>
  <data name="ProdutoSinonimo_DescricaoExistente" xml:space="preserve">
    <value>Já existe um sinônimo com a descrição {0}. Guid do sinônimo: {1}</value>
  </data>
  <data name="Produto_NomeSinonimoExistente" xml:space="preserve">
    <value>There is already a Product/Synonym with the description {0}</value>
  </data>
  <data name="ProdutoMensagem_IdNaoEncontrado" xml:space="preserve">
    <value>Unable to find any product message with id: {0}</value>
  </data>
  <data name="Mensagem_GuidNaoEncontrado" xml:space="preserve">
    <value>No messages found with guid {0}</value>
  </data>
  <data name="ProdutoMateriaPrima_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any raw material products with guid: {0}</value>
  </data>
  <data name="PesoMolecularBase_Invalido" xml:space="preserve">
    <value>The molecular weight of the base should be less than or equal to the molecular weight of the salt.</value>
  </data>
  <data name="CombinacaoPellets_QspExcipienteInvalida" xml:space="preserve">
    <value>Raw material cannot be marked as qsp, excipient or pellets.</value>
  </data>
  <data name="ProdutoMateriaPrima_NaoEhExcipiente" xml:space="preserve">
    <value>Selected product is not an excipient.</value>
  </data>
  <data name="ProdutoMateriaPrimaCapsulaPronta_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any products raw material capsule ready with guid: {0}</value>
  </data>
  <data name="ProdutoIncompativel_GuidNaoEncontrado" xml:space="preserve">
    <value>No incompatible products found with guid {0}</value>
  </data>
  <data name="Excipiente_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any excipient with guid: {0}</value>
  </data>
  <data name="ProdutoMateriaPrima_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any raw material products with id: {0}</value>
  </data>
  <data name="Excipiente_JaCadastrado" xml:space="preserve">
    <value>Excipient already registered with the following properties: Biofarmaceutica: {0}, Priority: {1}</value>
  </data>
  <data name="ProdutoEmbalagem_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any product packaging with guid: {0}</value>
  </data>
  <data name="Produto_DosagemInvalida" xml:space="preserve">
    <value>The product has an invalid dosage.</value>
  </data>
  <data name="ProdutoDiluido_GuidNaoEncontrado" xml:space="preserve">
    <value>No diluted product found with the guid {0}</value>
  </data>
  <data name="ProdutoCapsulaPronta_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any ready-made capsule products with guid: {0}</value>
  </data>
  <data name="ProdutoAssociado_GuidNaoEncontrado" xml:space="preserve">
    <value>No associated product found with guid {0}</value>
  </data>
  <data name="Cas_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any cas with Id: {0}</value>
  </data>
  <data name="Dcb_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any dcb with Id: {0}</value>
  </data>
  <data name="FormaFarmaceutica_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any pharmaceutical form with guid: {0}</value>
  </data>
  <data name="Posologia_DescricaoExistente" xml:space="preserve">
    <value>There is already a posology registered with the description: {0}</value>
  </data>
  <data name="Posologia_GuidNaoEncontrado" xml:space="preserve">
    <value>No posology found with guid {0}</value>
  </data>
  <data name="FormulaPadraoItem_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any item default formula with guid: {0}</value>
  </data>
  <data name="Laboratorio_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find lab with Id {0}</value>
  </data>
  <data name="LocalEstoque_DevePertencerAMesmaEmpresa" xml:space="preserve">
    <value>The Stock Location with Guid: {0} must belong to the selected company.</value>
  </data>
  <data name="FormulaPadrao_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any default formula with guid: {0}</value>
  </data>
  <data name="FormaFarmaceutica_GuidEstaAtiva" xml:space="preserve">
    <value>Pharmaceutical form with id: {0}, is active and cannot be deleted.</value>
  </data>
  <data name="FormaFarmaceutica_DescricaoExistente" xml:space="preserve">
    <value>There is already a pharmaceutical form with the description {0}</value>
  </data>
  <data name="EmbalagemClassificacaoFormaFarmaceutica_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find pharmaceutical form classification packaging with guid: {0}</value>
  </data>
  <data name="VinculoEmbalagemFormaFarmaceutica_JaExiste" xml:space="preserve">
    <value>There is already a link created between the Packaging Classification {0} and this Pharmaceutical Form.</value>
  </data>
  <data name="EmbalagemClassificacao_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any packaging rating with guid: {0}</value>
  </data>
  <data name="CapsulaCor_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any color capsules with guid: {0}</value>
  </data>
  <data name="Grupo_ExclusaoProibidaSubGrupo" xml:space="preserve">
    <value>The Group cannot be deleted because it is linked to {0} subgroup(s).</value>
  </data>
  <data name="Empresa_RemoverComUsuario" xml:space="preserve">
    <value>Could not remove the company with guid: {0} because contains {1} user(s) linked.</value>
  </data>
  <data name="Permissao_AdminSetupTenantExecutar" xml:space="preserve">
    <value>Execute tenant initial load.</value>
  </data>
  <data name="Permissao_AdminSetupTenantVerIdiomas" xml:space="preserve">
    <value>Show available languages.</value>
  </data>
  <data name="Permissao_Inexistente" xml:space="preserve">
    <value>Could not find permission with id: {0}</value>
  </data>
  <data name="Empresa_ComDependencia" xml:space="preserve">
    <value>It was not possible to delete the Company with Guid: {0} it already has movement.</value>
  </data>
  <data name="TransferenciaLoteItem_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Batch Transfer Item with guid: {0}</value>
  </data>
  <data name="TransferenciaLote_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Batch Transfers with guid: {0}</value>
  </data>
  <data name="Empresa_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any company with id: {0}</value>
  </data>
  <data name="FormaFarmaceutica_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any pharmaceutical form with id: {0}</value>
  </data>
  <data name="FornecedorContato_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any supplier with guid {0} contact.</value>
  </data>
  <data name="FornecedorContato_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any Supplier Account with id: {0}</value>
  </data>
  <data name="FornecedorDocumento_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any supplier with guid: {0} document.</value>
  </data>
  <data name="FornecedorDocumento_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any Supplier with Document with id: {0}</value>
  </data>
  <data name="FornecedorEndereco_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any supplier with guid: {0} adress.</value>
  </data>
  <data name="FornecedorEndereco_IdNaoEncontrado" xml:space="preserve">
    <value>Unable to find Supplier with Address id: {0}</value>
  </data>
  <data name="Fornecedor_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find a Supplier with id: {0}</value>
  </data>
  <data name="Grupo_GuidGrupoPaiNaoEncontrado" xml:space="preserve">
    <value>Could not find any Parent Group with id: {0}</value>
  </data>
  <data name="Grupo_IdGrupoPaiNaoEncontrado" xml:space="preserve">
    <value>Could not find a Parent Group with id: {0}</value>
  </data>
  <data name="LocalEstoque_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any Stock Location with id: {0}</value>
  </data>
  <data name="LocalEstoque_NaoPodeSerIgual" xml:space="preserve">
    <value>The Stock Location cannot be the same.</value>
  </data>
  <data name="LocalEstoque_NaoPossuiOProduto" xml:space="preserve">
    <value>The Stock Location does not have the Product.</value>
  </data>
  <data name="Lote_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any Lot with id: {0}</value>
  </data>
  <data name="Ncm_CodigoNaoEncontrado" xml:space="preserve">
    <value>Could not find NCM code.</value>
  </data>
  <data name="Ncm_DescricaoNaoEncontrada" xml:space="preserve">
    <value>Could not find NCM description.</value>
  </data>
  <data name="Ncm_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find NCM Id.</value>
  </data>
  <data name="NotaFiscalEntradaItem_JaLancado" xml:space="preserve">
    <value>Entry invoice of the item already posted.</value>
  </data>
  <data name="NotaFiscalEntradaItem_NaoEncontrado" xml:space="preserve">
    <value>Could not find the entry Invoice of the item.</value>
  </data>
  <data name="NotaFiscalEntradaLote_JaLancado" xml:space="preserve">
    <value>A batch entry invoice already posted.</value>
  </data>
  <data name="NotaFiscalEntradaLote_QuantidadeDivergente" xml:space="preserve">
    <value>Deviating quantity in the batch entry invoice.</value>
  </data>
  <data name="NotaFiscalEntrada_Finalizada" xml:space="preserve">
    <value>Completed entry Invoice.</value>
  </data>
  <data name="NotaFiscalEntrada_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find Incoming Invoice with guid: {0}</value>
  </data>
  <data name="NotaFiscalEntrada_JaLancada" xml:space="preserve">
    <value>Entry invoice already posted.</value>
  </data>
  <data name="OperacaoEstoque_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any stock operation with id: {0}</value>
  </data>
  <data name="PedidoCompraItem_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find the guid: {0} of the item's Purchase Order.</value>
  </data>
  <data name="PedidoCompra_Cancelado" xml:space="preserve">
    <value>Canceled Purchase order.</value>
  </data>
  <data name="PedidoCompra_Guid_NaoEncontrado" xml:space="preserve">
    <value>Could not find the guid: {0} of the Purchase Order.</value>
  </data>
  <data name="PedidoCompra_Reprovado" xml:space="preserve">
    <value>Disapproved Purchase order.</value>
  </data>
  <data name="ProdutoTipoCapsula_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not fint any capsule type product with guid {0}</value>
  </data>
  <data name="Produto_GuidJaExisteNoPedidoDeCompra" xml:space="preserve">
    <value>A Product with guid: {0} already exists in the purchase order.</value>
  </data>
  <data name="Produto_IdNaoEncontrado" xml:space="preserve">
    <value>No Product found with id {0}</value>
  </data>
  <data name="TipoContato_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any contact type with id: {0}</value>
  </data>
  <data name="TipoDocumento_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any document type with id: {0}</value>
  </data>
  <data name="TipoFornecedor_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any supplier type with id: {0}</value>
  </data>
  <data name="TipoFrete_CodigoNaoEncontrado" xml:space="preserve">
    <value>Could not find any Freight type code.</value>
  </data>
  <data name="UnidadeMedida_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any Unit of Measure with id: {0}</value>
  </data>
  <data name="GrupoProduto_DescricaoExistente" xml:space="preserve">
    <value>There is already a Group with the description {0}</value>
  </data>
  <data name="GrupoProduto_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Group with the guid: {0}</value>
  </data>
  <data name="Usuario_ComDependencias" xml:space="preserve">
    <value>The user has dependencies and cannot be deleted.</value>
  </data>
  <data name="Conglomerado_EmUso" xml:space="preserve">
    <value>Cluster {0} cannot be excluded as it is related to other companies.</value>
  </data>
  <data name="NaturezaOperacao_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any type of operation with id: {0}</value>
  </data>
  <data name="Grupo_ComDependencia" xml:space="preserve">
    <value>It was not possible to delete the Group with Guid: {0} it already has movement.</value>
  </data>
  <data name="TipoDocumento_Invalido" xml:space="preserve">
    <value>Invalid document type entered.</value>
  </data>
  <data name="Fornecedor_ComDependencia" xml:space="preserve">
    <value>It was not possible to delete the Supplier with Guid: {0} it already has movement.</value>
  </data>
  <data name="Lote_BloqueadoOuInativo" xml:space="preserve">
    <value>Batch {0} is Blocked or Inactive.</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerEstornado" xml:space="preserve">
    <value>Could not reverse Purchase Order {0}</value>
  </data>
  <data name="SubGrupo_GuidNaoEncontrado" xml:space="preserve">
    <value>No Subgroups found with guid {0}</value>
  </data>
  <data name="Prescritor_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find a prescriber with guid: {0}</value>
  </data>
  <data name="TipoRegistro_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any register type with id: {0}</value>
  </data>
  <data name="Prescritor_JaCadastrado" xml:space="preserve">
    <value>Prescriber already registered with the following property: CodigoRegistro: {0}</value>
  </data>
  <data name="UnidadeMedida_Invalida" xml:space="preserve">
    <value>Unit of measure {0} does not belong to the product class.</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerConfirmadoComFornecedor" xml:space="preserve">
    <value>Unable to confirm Purchase Order {0} with supplier</value>
  </data>
  <data name="Cliente_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find a customer with guid: {0}</value>
  </data>
  <data name="Cliente_JaCadastrado" xml:space="preserve">
    <value>Customer already registered with the following property: Codigo: {0}</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerReprovado" xml:space="preserve">
    <value>Unable to disapprove Purchase Order {0}</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerCancelado" xml:space="preserve">
    <value>Unable to cancel Purchase Order {0}</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerAprovado" xml:space="preserve">
    <value>Unable to approve Purchase Order {0}</value>
  </data>
  <data name="TipoRegistro_JaCadastrado" xml:space="preserve">
    <value>There is already a {0} with the code {1} and state {2} registered.</value>
  </data>
  <data name="ClienteDocumento_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any customer document with id: {0}</value>
  </data>
  <data name="ClienteEndereco_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any customer adress with id: {0}</value>
  </data>
  <data name="PrescritorEndereco_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any prescriber with guid: {0} adress.</value>
  </data>
  <data name="ClienteDocumento_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any customer document with guid: {0}</value>
  </data>
  <data name="ClienteContato_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any customer contact with guid: {0}</value>
  </data>
  <data name="ClienteEndereco_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any customer adress with guid: {0}</value>
  </data>
  <data name="PrescritorContato_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any prescriber with guid {0} contact.</value>
  </data>
  <data name="PrescritorDocumento_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any prescriber with guid: {0} document.</value>
  </data>
  <data name="Cliente_ComDependencia" xml:space="preserve">
    <value>It was not possible to delete the Client with Guid: {0} it already has movement.</value>
  </data>
  <data name="Estado_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any State with guid: {0}</value>
  </data>
  <data name="Cidade_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any City with guid: {0}</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraVerDetalhes" xml:space="preserve">
    <value>See purchase orders details.</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraVerLista" xml:space="preserve">
    <value>See purchase orders list.</value>
  </data>
  <data name="Permissao_ComprasPedidosCompra" xml:space="preserve">
    <value>Purchase Orders</value>
  </data>
  <data name="Permissao_AdministrativoManager" xml:space="preserve">
    <value>Access to Bootis Admin Areas</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraAprovar" xml:space="preserve">
    <value>Approve purchase orders</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraCadastrar" xml:space="preserve">
    <value>Register new purchase orders</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraCancelar" xml:space="preserve">
    <value>Cancel purchase orders</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraConfirmarComFornecedor" xml:space="preserve">
    <value>Confirm purchase orders with the supplier</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraEditarDetalhes" xml:space="preserve">
    <value>Edit purchase order details</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraEstornarConfirmadoComFornecedor" xml:space="preserve">
    <value>Reverse confirmed purchase orders with the supplier</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraEstornarLiberado" xml:space="preserve">
    <value>Reverse approved purchase orders</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraExcluir" xml:space="preserve">
    <value>Delete purchase orders</value>
  </data>
  <data name="Permissao_ComprasPedidoCompraReprovar" xml:space="preserve">
    <value>Disapprove purchase orders</value>
  </data>
  <data name="Permissao_Vendas" xml:space="preserve">
    <value>Sells</value>
  </data>
  <data name="Permissao_VendasClientes" xml:space="preserve">
    <value>Customers</value>
  </data>
  <data name="Permissao_VendasClientesAlterarStatus" xml:space="preserve">
    <value>Chance customers status</value>
  </data>
  <data name="Permissao_VendasClientesCadastrar" xml:space="preserve">
    <value>Register new customers</value>
  </data>
  <data name="Permissao_VendasClientesEditarContatos" xml:space="preserve">
    <value>Edit customers contacts</value>
  </data>
  <data name="Permissao_VendasClientesEditarDetalhes" xml:space="preserve">
    <value>Edit customers details</value>
  </data>
  <data name="Permissao_VendasClientesEditarDocumentos" xml:space="preserve">
    <value>Edit customers documents</value>
  </data>
  <data name="Permissao_VendasClientesEditarEnderecos" xml:space="preserve">
    <value>Edit customers adresses</value>
  </data>
  <data name="Permissao_VendasClientesExcluir" xml:space="preserve">
    <value>Exclude customers</value>
  </data>
  <data name="Permissao_VendasClientesVerDetalhes" xml:space="preserve">
    <value>View customers details</value>
  </data>
  <data name="Permissao_VendasClientesVerLista" xml:space="preserve">
    <value>View customers list</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritor" xml:space="preserve">
    <value>Prescriber Specialties</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorAlterarStatus" xml:space="preserve">
    <value>Change prescriber specialties status</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorCadastrar" xml:space="preserve">
    <value>Register new prescriber specialties</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorEditarDetalhes" xml:space="preserve">
    <value>Edit prescriber specialties details</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorExcluir" xml:space="preserve">
    <value>Exclude prescriber specialties</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorVerDetalhes" xml:space="preserve">
    <value>See prescriber specialties</value>
  </data>
  <data name="Permissao_VendasEspecialidadePrescritorVerLista" xml:space="preserve">
    <value>See prescriber specialties list</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaAlterarStatus" xml:space="preserve">
    <value>Change status of incoming invoices</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaCadastrar" xml:space="preserve">
    <value>Register new incoming invoices</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaEditarDetalhes" xml:space="preserve">
    <value>Edit details of A/P invoices</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaExcluir" xml:space="preserve">
    <value>Delete incoming invoices</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaLancarLotes" xml:space="preserve">
    <value>Post batches of A/P invoices</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaVerDetalhes" xml:space="preserve">
    <value>View details of incoming invoices</value>
  </data>
  <data name="Permissao_ComprasNotaFiscalEntradaVerLista" xml:space="preserve">
    <value>View list of incoming invoices</value>
  </data>
  <data name="Permissao_ComprasNotasFiscaisEntrada" xml:space="preserve">
    <value>Entry Invoices</value>
  </data>
  <data name="Permissao_VendasPrescritoresAlterarStatus" xml:space="preserve">
    <value>Change status of prescribers</value>
  </data>
  <data name="Permissao_VendasPrescritoresCadastrar" xml:space="preserve">
    <value>Cadastrar Prescritores</value>
  </data>
  <data name="Permissao_VendasPrescritoresEditarContatos" xml:space="preserve">
    <value>Edit prescriber contacts</value>
  </data>
  <data name="Permissao_VendasPrescritoresEditarDetalhes" xml:space="preserve">
    <value>Edit prescriber details</value>
  </data>
  <data name="Permissao_VendasPrescritoresEditarDocumentos" xml:space="preserve">
    <value>Edit prescriber documents</value>
  </data>
  <data name="Permissao_VendasPrescritoresEditarEnderecos" xml:space="preserve">
    <value>Edit prescriber addresses</value>
  </data>
  <data name="Permissao_VendasPrescritoresExcluir" xml:space="preserve">
    <value>Exclude prescribers</value>
  </data>
  <data name="Permissao_VendasPrescritoresVerDetalhes" xml:space="preserve">
    <value>View prescriber details</value>
  </data>
  <data name="Permissao_VendasPrescritoresVerLista" xml:space="preserve">
    <value>View list of prescribers</value>
  </data>
  <data name="Permissao_VendasPrescritores" xml:space="preserve">
    <value>Prescribers</value>
  </data>
  <data name="Cep_NaoEncontrado" xml:space="preserve">
    <value>The zip code: {0} was not found</value>
  </data>
  <data name="ClienteContato_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any customer contact with id: {0}</value>
  </data>
  <data name="CapsulaCor_DescricaoExistente" xml:space="preserve">
    <value>Another Color Capsule with the description {0} already exists.</value>
  </data>
  <data name="EmbalagemClassificacao_DescricaoExistente" xml:space="preserve">
    <value>Another Packaging Classification with description {0} already exists.</value>
  </data>
  <data name="Prescritor_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any Prescriber with id: {0}</value>
  </data>
  <data name="Laboratorio_DescricaoExistente" xml:space="preserve">
    <value>There is already a Laboratory registered with the description {0} for this Company.</value>
  </data>
  <data name="ProdutoLoteEmUso_JaAssociado" xml:space="preserve">
    <value>Batch already reported in use for this product and stock location.</value>
  </data>
  <data name="ProdutoLoteEmUso_NaoExiste" xml:space="preserve">
    <value>Could not find this batch associated with the product and stock location.</value>
  </data>
  <data name="Permissao_Producao" xml:space="preserve">
    <value>Production</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCor" xml:space="preserve">
    <value>Capsule color</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCorCadastrar" xml:space="preserve">
    <value>Register new capsule colors</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCorEditarDetalhes" xml:space="preserve">
    <value>Edit capsule colors details</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCorExcluir" xml:space="preserve">
    <value>Delete capsule colors</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCorVerDetalhes" xml:space="preserve">
    <value>View capsule color details</value>
  </data>
  <data name="Permissao_ProducaoCapsulaCorVerLista" xml:space="preserve">
    <value>View list of capsule colors</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemAlterarStatus" xml:space="preserve">
    <value>Change status of packaging classifications</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemCadastrar" xml:space="preserve">
    <value>Register new packaging classifications</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemEditarDetalhes" xml:space="preserve">
    <value>Edit packaging classifications details</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemExcluir" xml:space="preserve">
    <value>Delete packaging classifications</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemVerDetalhes" xml:space="preserve">
    <value>View details of packaging classifications</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagemVerLista" xml:space="preserve">
    <value>View list of packaging classifications</value>
  </data>
  <data name="Permissao_ProducaoClassificacaoEmbalagem" xml:space="preserve">
    <value>Packaging classification</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceutica" xml:space="preserve">
    <value>Pharmaceutical form</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaAlterarStatus" xml:space="preserve">
    <value>Change status of pharmaceutical forms</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaCadastrar" xml:space="preserve">
    <value>Register new pharmaceutical forms</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaEditarDetalhes" xml:space="preserve">
    <value>Edit pharmaceutical forms details</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaExcluir" xml:space="preserve">
    <value>Delete pharmaceutical forms</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaVerDetalhes" xml:space="preserve">
    <value>View details of pharmaceutical forms</value>
  </data>
  <data name="Permissao_ProducaoFormaFarmaceuticaVerLista" xml:space="preserve">
    <value>View list of pharmaceutical forms</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadrao" xml:space="preserve">
    <value>Standard formula</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadraoCadastrar" xml:space="preserve">
    <value>Register new standard formulas</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadraoEditarDetalhes" xml:space="preserve">
    <value>Edit details of standard formulas</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadraoExcluir" xml:space="preserve">
    <value>Delete standard formulas</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadraoVerDetalhes" xml:space="preserve">
    <value>View details of standard formulas</value>
  </data>
  <data name="Permissao_ProducaoFormulaPadraoVerLista" xml:space="preserve">
    <value>View list of standard formulas</value>
  </data>
  <data name="Permissao_ProducaoLaboratorio" xml:space="preserve">
    <value>Laboratory</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioAlterarStatus" xml:space="preserve">
    <value>Change status of laboratories</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioCadastrar" xml:space="preserve">
    <value>Register new laboratories</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioEditarDetalhes" xml:space="preserve">
    <value>Edit laboratories details</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioExcluir" xml:space="preserve">
    <value>Delete laboratories</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioVerDetalhes" xml:space="preserve">
    <value>View laboratories details</value>
  </data>
  <data name="Permissao_ProducaoLaboratorioVerLista" xml:space="preserve">
    <value>View list of laboratories</value>
  </data>
  <data name="Permissao_ProducaoPosologia" xml:space="preserve">
    <value>Posology</value>
  </data>
  <data name="Permissao_ProducaoPosologiaAlterarStatus" xml:space="preserve">
    <value>Change posologies status</value>
  </data>
  <data name="Permissao_ProducaoPosologiaCadastrar" xml:space="preserve">
    <value>Register new posologies</value>
  </data>
  <data name="Permissao_ProducaoPosologiaEditarDetalhes" xml:space="preserve">
    <value>Edit posologies details</value>
  </data>
  <data name="Permissao_ProducaoPosologiaExcluir" xml:space="preserve">
    <value>Delete posologies</value>
  </data>
  <data name="Permissao_ProducaoPosologiaVerDetalhes" xml:space="preserve">
    <value>View posologies details</value>
  </data>
  <data name="Permissao_ProducaoPosologiaVerLista" xml:space="preserve">
    <value>View list of posologies</value>
  </data>
  <data name="StatusAtendimento_DescricaoExistente" xml:space="preserve">
    <value>There is already a service status registered with the description {0}</value>
  </data>
  <data name="StatusAtendimento_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Service Status with guid: {0}</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerEditado" xml:space="preserve">
    <value>Unable to edit Purchase Order {0} with status {1}</value>
  </data>
  <data name="EspecialidadePrescritor_DescricaoExistente" xml:space="preserve">
    <value>There is already a prescriber specialty with the description: {0}</value>
  </data>
  <data name="EspecialidadePrescritor_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any prescriber specialty with the guid: {0}</value>
  </data>
  <data name="CapsulaTamanho_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find a capsule size with id: {0}</value>
  </data>
  <data name="EmbalagemAssociacao_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any packaging associations with guid: {0}</value>
  </data>
  <data name="EmbalagemCapsulaAssociacao_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any capsule packaging association with guid: {0}</value>
  </data>
  <data name="Atendimento_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Service with guid: {0}</value>
  </data>
  <data name="CstCsosn_NaoEncontrado" xml:space="preserve">
    <value>Cst/Csosn , id: {0} not found.</value>
  </data>
  <data name="TipoDocumento_JaCadastrado" xml:space="preserve">
    <value>There is already a type of document with id {0} registered.</value>
  </data>
  <data name="PedidoCompraItem_ValorDescontoInvalido" xml:space="preserve">
    <value>Discount value {0} cannot be greater than price {1}</value>
  </data>
  <data name="ProdutoEmbalagem_JaVinculado" xml:space="preserve">
    <value>Product {0} is already linked to a Packaging Classification.</value>
  </data>
  <data name="PedidoCompra_NaoPodeSerBaixado" xml:space="preserve">
    <value>Purchase order {0} cannot be finalized.</value>
  </data>
  <data name="Validation_ValorInvalido" xml:space="preserve">
    <value>The value for {0} must be within the range of {1} and {2}.</value>
  </data>
  <data name="FormulaPadrao_ProdutoExistente" xml:space="preserve">
    <value>There is already a Standard Formula for this Product {0}</value>
  </data>
  <data name="PedidoVendaItem_ValorDescontoInvalido" xml:space="preserve">
    <value>The value of the discount cannot exceed the value of the item.</value>
  </data>
  <data name="PedidoVenda_GuidNaoEncontrado" xml:space="preserve">
    <value>Unable to find a sales order with guid: {0}</value>
  </data>
  <data name="PedidoVenda_NaoPodeSerAprovado" xml:space="preserve">
    <value>Unable to approve sales order {0}</value>
  </data>
  <data name="PedidoVenda_NaoPodeSerReprovado" xml:space="preserve">
    <value>Unable to disapprove sales order {0}</value>
  </data>
  <data name="PedidoVenda_NaoPodeSerEstornado" xml:space="preserve">
    <value>Could not reverse sales order {0}</value>
  </data>
  <data name="Atendimento_PedidoVendaExistente" xml:space="preserve">
    <value>There is already a sales order linked to this service.</value>
  </data>
  <data name="PedidoVenda_ValorTotalInvalido" xml:space="preserve">
    <value>It is not possible to enter a total value greater than the total net value of the items.</value>
  </data>
  <data name="PedidoVenda_SomaDescontosInvalida" xml:space="preserve">
    <value>It is not allowed to enter a discount that is less than the sum of the products unit discounts.</value>
  </data>
  <data name="PedidoVenda_ValorDescontoInvalido" xml:space="preserve">
    <value>The discount amount cannot exceed the order value.</value>
  </data>
  <data name="SaldoInformado_Invalido" xml:space="preserve">
    <value>Invalid balance entered.</value>
  </data>
  <data name="Permissao_VendasAtendimentosCadastrar" xml:space="preserve">
    <value>Register new services</value>
  </data>
  <data name="Permissao_VendasAtendimentosVerDetalhes" xml:space="preserve">
    <value>View service details</value>
  </data>
  <data name="Permissao_VendasAtendimentosVerLista" xml:space="preserve">
    <value>See list of services</value>
  </data>
  <data name="Permissao_VendasPedidosVendaAprovar" xml:space="preserve">
    <value>Approve sales orders</value>
  </data>
  <data name="Permissao_VendasPedidosVendaCadastrar" xml:space="preserve">
    <value>Register new sales orders</value>
  </data>
  <data name="Permissao_VendasPedidosVendaCancelar" xml:space="preserve">
    <value>Cancel sales orders</value>
  </data>
  <data name="Permissao_VendasPedidosVendaEditarDetalhes" xml:space="preserve">
    <value>Edit sales order details</value>
  </data>
  <data name="Permissao_VendasPedidosVendaEstornar" xml:space="preserve">
    <value>Reverse sales orders</value>
  </data>
  <data name="Permissao_VendasPedidosVendaReprovar" xml:space="preserve">
    <value>Reject sales orders</value>
  </data>
  <data name="Permissao_VendasPedidosVendaVerDetalhes" xml:space="preserve">
    <value>View sales order details</value>
  </data>
  <data name="Permissao_VendasPedidosVendaVerLista" xml:space="preserve">
    <value>View sales order list</value>
  </data>
  <data name="Permissao_VendasAtendimentos" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="Permissao_VendasPedidosVenda" xml:space="preserve">
    <value>Sales order</value>
  </data>
  <data name="NotaFiscalEntrada_NaoPodeSerLancada" xml:space="preserve">
    <value>The invoice {0} has a status other than Pending and cannot be posted.</value>
  </data>
  <data name="VinculoEmbalagemFormaFarmaceutica_Duplicado" xml:space="preserve">
    <value>It is not possible to include Packaging Classification {0} more than once in the same Pharmaceutical Form</value>
  </data>
  <data name="MensagemDescricao_JaExiste" xml:space="preserve">
    <value>A product message with this description already exists.</value>
  </data>
  <data name="ProdutoMensagem_NaoVinculado" xml:space="preserve">
    <value>Product not linked to message.</value>
  </data>
  <data name="ProdutoMensagem_JaVinculado" xml:space="preserve">
    <value>Product already linked to the message.</value>
  </data>
  <data name="NotaFiscalEntradaLote_NaoPodeSerRemovido" xml:space="preserve">
    <value>Incoming Invoice Batches {0} cannot be deleted, because they have already been posted.</value>
  </data>
  <data name="PedidoVenda_StatusInvalido" xml:space="preserve">
    <value>It is not possible to change a sales order with status: {0}.</value>
  </data>
  <data name="ProdutoAssociado_ProdutoInvalido" xml:space="preserve">
    <value>The selected product does not fall into the accepted classes to associate with a product.</value>
  </data>
  <data name="ProdutoAssociado_VinculoExistente" xml:space="preserve">
    <value>There is already an association created between Product {0} and Product {1} with Pharmaceutical Form {2}.</value>
  </data>
  <data name="Permissao_EstoqueMensagemProduto" xml:space="preserve">
    <value>Product Message</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoCadastrar" xml:space="preserve">
    <value>Register new product messages</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoEditarDetalhes" xml:space="preserve">
    <value>Edit product message details</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoExcluir" xml:space="preserve">
    <value>Delete product messages</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoRemoverProduto" xml:space="preserve">
    <value>Remove product from a product message</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoVerDetalhes" xml:space="preserve">
    <value>View product message details</value>
  </data>
  <data name="Permissao_EstoqueMensagemProdutoVerLista" xml:space="preserve">
    <value>View list of product messages</value>
  </data>
  <data name="ProdutoDiluido_ProdutoInvalido" xml:space="preserve">
    <value>The product class {1} is not Raw Material and therefore cannot be diluted.</value>
  </data>
  <data name="ProdutoIncompativel_ProdutoInvalido" xml:space="preserve">
    <value>The product class is not Raw Material and therefore cannot be incompatible.</value>
  </data>
  <data name="ProdutoIncompativel_VinculoExistente" xml:space="preserve">
    <value>There is already an incompatibility created between Product {0} and Product {1}.</value>
  </data>
  <data name="ProdutoSinonimo_SinonimoIgualAoProduto" xml:space="preserve">
    <value>A product with description {0} already exists. Product Guid: {1}</value>
  </data>
  <data name="ProdutoSinonimo_ProdutoInvalido" xml:space="preserve">
    <value>The product class is not Raw Material and therefore it is not possible to register a synonym.</value>
  </data>
  <data name="FormaFarmaceutica_ApresentacaoExistente" xml:space="preserve">
    <value>There is already a pharmaceutical form with the presentation {0}</value>
  </data>
  <data name="Permissao_EstoqueInformacoesTecnicasVisualizar" xml:space="preserve">
    <value>View technical information</value>
  </data>
  <data name="Permissao_EstoqueProdutosAssociadoCadastrar" xml:space="preserve">
    <value>Register new associated products</value>
  </data>
  <data name="Permissao_EstoqueProdutosAssociadoEditarDetalhes" xml:space="preserve">
    <value>Edit associated products</value>
  </data>
  <data name="Permissao_EstoqueProdutosAssociadoExcluir" xml:space="preserve">
    <value>Delete associated products</value>
  </data>
  <data name="Permissao_EstoqueProdutosAssociadoVisualizar" xml:space="preserve">
    <value>View associated products</value>
  </data>
  <data name="Permissao_EstoqueProdutosDiluidoCadastrar" xml:space="preserve">
    <value>Register new diluted products</value>
  </data>
  <data name="Permissao_EstoqueProdutosDiluidoEditarDetalhes" xml:space="preserve">
    <value>Edit diluted products</value>
  </data>
  <data name="Permissao_EstoqueProdutosDiluidoExcluir" xml:space="preserve">
    <value>Exclude diluted products</value>
  </data>
  <data name="Permissao_EstoqueProdutosDiluidoVisualizar" xml:space="preserve">
    <value>View diluted products</value>
  </data>
  <data name="Permissao_EstoqueProdutosIncompativelCadastrar" xml:space="preserve">
    <value>Register incompatible products</value>
  </data>
  <data name="Permissao_EstoqueProdutosIncompativelEditarDetalhes" xml:space="preserve">
    <value>Edit incompatible products</value>
  </data>
  <data name="Permissao_EstoqueProdutosIncompativelExcluir" xml:space="preserve">
    <value>Delete incompatible products</value>
  </data>
  <data name="Permissao_EstoqueProdutosIncompativelVisualizar" xml:space="preserve">
    <value>View incompatible products</value>
  </data>
  <data name="Permissao_EstoqueProdutosSinonimoCadastrar" xml:space="preserve">
    <value>Register new synonyms</value>
  </data>
  <data name="Permissao_EstoqueProdutosSinonimoEditarDetalhes" xml:space="preserve">
    <value>Edit synonyms</value>
  </data>
  <data name="Permissao_EstoqueProdutosSinonimoExcluir" xml:space="preserve">
    <value>Exclude synonyms</value>
  </data>
  <data name="Permissao_EstoqueProdutosSinonimoVisualizar" xml:space="preserve">
    <value>View synonyms</value>
  </data>
  <data name="SaldoEstoqueLocal_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Inventory Balance with Lot guid: {0} and Local Stock guid: {1}</value>
  </data>
  <data name="NotaFiscalEntradaLote_UnidadeMedidaInvalidaParaClasseProduto" xml:space="preserve">
    <value>UnitMeasureId {1} is incompatible with ClassProdutoId {2}</value>
  </data>
  <data name="Permissao_EstoqueProdutosMensagemCadastrar" xml:space="preserve">
    <value>Link messages to the product</value>
  </data>
  <data name="Permissao_EstoqueProdutosMensagemExcluir" xml:space="preserve">
    <value>Delete product messages</value>
  </data>
  <data name="Permissao_EstoqueProdutosMensagemVisualizar" xml:space="preserve">
    <value>View product messages</value>
  </data>
  <data name="FormulaPadraoItem_QuantidadeInvalida" xml:space="preserve">
    <value>Quantity invalid for the Product.</value>
  </data>
  <data name="Usuario_GrupoRequerido" xml:space="preserve">
    <value>The user must be assigned to at least one group.</value>
  </data>
  <data name="ProdutoDiluido_DosagemObrigatoria" xml:space="preserve">
    <value>Dosage fields are mandatory when the Pharmaceutical Form is null.</value>
  </data>
  <data name="FormaFarmaceutica_UnidadeMedidaInvalida" xml:space="preserve">
    <value>The Unit of Measure {0} is invalid in the context of Pharmaceutical Form.</value>
  </data>
  <data name="ProdutoDiluido_DosagemInvalida" xml:space="preserve">
    <value>Dilution {0} existing with minimum dosage {1} and maximum  dosage {2} within the range of minimum dosage {3} and maximum dosage {4} for the unit of measurement {5}.</value>
  </data>
  <data name="Grupo_UsuarioNaoEncontrado" xml:space="preserve">
    <value>No users with Guid {0} in group {1} were found</value>
  </data>
  <data name="Lote_NaoPossuiInformacaoTecnica" xml:space="preserve">
    <value>Lot {0} does not have Technical Information.</value>
  </data>
  <data name="Embalagem_NaoDisponivel" xml:space="preserve">
    <value>There is no packaging available for this recipe configuration.</value>
  </data>
  <data name="Capsula_NaoDisponivel" xml:space="preserve">
    <value>There is no capsule available for this recipe configuration.</value>
  </data>
  <data name="Conglomerado_NomeJaCadastrado" xml:space="preserve">
    <value>This name is already being used</value>
  </data>
  <data name="ProdutoSinonimo_ProdutoNaoVinculado" xml:space="preserve">
    <value>There is no link between the synonym with guid {0} and the product with guid {1}</value>
  </data>
  <data name="Produto_ComMovimentacao" xml:space="preserve">
    <value>Product {0} has movement in the system, for this reason it is not possible to change the product class.</value>
  </data>
  <data name="ProdutoAssociado_RecursaoDetectada" xml:space="preserve">
    <value>Recursion detected in associated products, check your registration</value>
  </data>
  <data name="Inventario_NaoPodeSerAtualizado" xml:space="preserve">
    <value>Unable to edit Inventory {0}.</value>
  </data>
  <data name="Inventario_NaoPodeSerCancelado" xml:space="preserve">
    <value>Unable to cancel Inventory {0}.</value>
  </data>
  <data name="Inventario_NaoPodeIniciarLancamento" xml:space="preserve">
    <value>Inventory {0} cannot start the Launch because the current status does not correspond to "Waiting for Launch".</value>
  </data>
  <data name="SaldoEstoque_SaldoBloqueado" xml:space="preserve">
    <value>It is not possible to move the Stock Balance {0}, as the corresponding balance is blocked.</value>
  </data>
  <data name="ReceitaManipulada_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any compounded prescription with guid: {0}</value>
  </data>
  <data name="PedidoVenda_NaoPodeSerCancelado" xml:space="preserve">
    <value>Could not cancel sales order {0}</value>
  </data>
  <data name="Fornecedor_CnpjExistente" xml:space="preserve">
    <value>There is already another Supplier with CNPJ {0} registered.</value>
  </data>
  <data name="Fornecedor_CpfExistente" xml:space="preserve">
    <value>There is already another Supplier with CPF {0} registered.</value>
  </data>
  <data name="Inventario_NaoPodeFinalizarLancamento" xml:space="preserve">
    <value>Unable to complete the release of Inventory {0} because the current status does not match Release.</value>
  </data>
  <data name="Inventario_InventarioItensIdsDuplicados" xml:space="preserve">
    <value>It is not possible to launch the same InventoryItemId more than once.</value>
  </data>
  <data name="Inventario_LancamentoComItensFaltando" xml:space="preserve">
    <value>It was not possible to complete the Inventory launch because there are products that have not been filled.</value>
  </data>
  <data name="Inventario_LancamentoNaoEncontrado" xml:space="preserve">
    <value>Could not find any InventoryLaunch with code {0} for Inventory {1} .</value>
  </data>
  <data name="Permissao_EstoqueInventarioCadastrar" xml:space="preserve">
    <value>Register new inventories</value>
  </data>
  <data name="Permissao_EstoqueInventarioExcluir" xml:space="preserve">
    <value>Delete inventories</value>
  </data>
  <data name="Permissao_EstoqueInventarioVerDetalhes" xml:space="preserve">
    <value>View inventory details</value>
  </data>
  <data name="Permissao_EstoqueInventarioVerDetalhesConferencia" xml:space="preserve">
    <value>View conference details</value>
  </data>
  <data name="Permissao_EstoqueInventarioVerLista" xml:space="preserve">
    <value>View inventory list</value>
  </data>
  <data name="Permissao_EstoqueInventario" xml:space="preserve">
    <value>Inventories</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipulada" xml:space="preserve">
    <value>Revenues</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaVerLista" xml:space="preserve">
    <value>See list of recipes</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaVerDetalhes" xml:space="preserve">
    <value>View recipe details</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaCadastrar" xml:space="preserve">
    <value>Register recipes</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaEditarDetalhes" xml:space="preserve">
    <value>Edit recipe details</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaAlterarStatus" xml:space="preserve">
    <value>Change prescription status</value>
  </data>
  <data name="Permissao_ProducaoReceitaManipuladaExcluir" xml:space="preserve">
    <value>Delete recipes</value>
  </data>
  <data name="Inventario_NaoPodeFinalizarConferencia" xml:space="preserve">
    <value>It is not possible to complete the Inventory {0} conference because the current status does not correspond to Conference.</value>
  </data>
  <data name="Inventario_LancamentoNaoPoderSerCancelado" xml:space="preserve">
    <value>The current release of Inventory {0} cannot be canceled.</value>
  </data>
  <data name="Inventario_NaoPodeAdicionarProduto" xml:space="preserve">
    <value>It is not possible to add products to the current release of Inventory {0} because it is not in the Release status.</value>
  </data>
  <data name="Inventario_ProdutoExistenteNoLancamento" xml:space="preserve">
    <value>Lot {0} exists in the current Inventory release {1}.</value>
  </data>
  <data name="Inventario_LocalEstoqueInexistente" xml:space="preserve">
    <value>Stock Location {0} does not belong to Inventory {1}.</value>
  </data>
  <data name="Inventario_CadastroTodosLocaisEstoqueDuplicado" xml:space="preserve">
    <value>It is not allowed to send more Stock Locations when trying to register an Inventory for all Stock Locations and Groups.</value>
  </data>
  <data name="ReceitaManipuladaItem_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any recipe component with id: {0}</value>
  </data>
  <data name="ReceitaManipuladaItem_FatoresNaoEncontrados" xml:space="preserve">
    <value>There are no factors for the product with id {0}</value>
  </data>
  <data name="Inventario_NaoPodeSerExcluido" xml:space="preserve">
    <value>Inventory {0} cannot be deleted because its current status does not match Waiting for Release.</value>
  </data>
  <data name="Cliente_CpfExistente" xml:space="preserve">
    <value>There is already a customer with CPF {0} registered.</value>
  </data>
  <data name="Cliente_CnpjExistente" xml:space="preserve">
    <value>There is already a customer with CNPJ {0} registered.</value>
  </data>
  <data name="Empresa_CnpjExistente" xml:space="preserve">
    <value>There is another Company registered with CNPJ {0}.</value>
  </data>
  <data name="PedidoVendaItem_ProdutoInativo" xml:space="preserve">
    <value>Unable to approve a sales order with inactive products</value>
  </data>
  <data name="Conglomerado_EmpresaNaoVinculada" xml:space="preserve">
    <value>Company {0} is not part of Conglomerate {1}.</value>
  </data>
  <data name="ReceitaManipuladaItem_OrdemInvalida" xml:space="preserve">
    <value>There is already a Recipe component with order {0}.</value>
  </data>
  <data name="SaldoEstoque_NaoEncontradoLoteId" xml:space="preserve">
    <value>Could not find any Stock Balance with Batch id: {0}</value>
  </data>
  <data name="Inventario_LocalEstoqueNaoVinculado" xml:space="preserve">
    <value>Stock Location {0} is not linked to Inventory {1}.</value>
  </data>
  <data name="ModeloRotulo_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any Label Template with guid: {0}</value>
  </data>
  <data name="ModeloRotulo_NomeJaExiste" xml:space="preserve">
    <value>A label template already exists for the chosen type with this description {0}</value>
  </data>
  <data name="ReceitaManipulada_QuantidadeReceitaInvalida" xml:space="preserve">
    <value>Invalid recipe quantity: null or zero value.</value>
  </data>
  <data name="ReceitaManipulada_ClasseBiofarmaceuticaInvalida" xml:space="preserve">
    <value>The biopharmaceutical class of the raw materials in the recipe could not be determined</value>
  </data>
  <data name="ReceitaManipulada_UnidadeMedidaQspInvalida" xml:space="preserve">
    <value>The QSP unit of measurement is not valid for this pharmaceutical form.</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloVerLista" xml:space="preserve">
    <value>See list of label templates</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloVerDetalhes" xml:space="preserve">
    <value>View label template details</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloCadastrar" xml:space="preserve">
    <value>Register new label models</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloEditarDetalhes" xml:space="preserve">
    <value>Edit label template details</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloExcluir" xml:space="preserve">
    <value>Delete label templates</value>
  </data>
  <data name="Permissao_ProducaoModeloRotuloAlterarStatus" xml:space="preserve">
    <value>Change Status labels templates</value>
  </data>
  <data name="Permissao_ProducaoModeloRotulo" xml:space="preserve">
    <value>Label Template</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoVerLista" xml:space="preserve">
    <value>See list of manipulation order templates</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoVerDetalhes" xml:space="preserve">
    <value>View details of manipulation order templates</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoCadastrar" xml:space="preserve">
    <value>Register new manipulation order templates</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoEditarDetalhes" xml:space="preserve">
    <value>Edit details of manipulation order templates</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoExcluir" xml:space="preserve">
    <value>Delete manipulation order templates</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacaoAlterarStatus" xml:space="preserve">
    <value>Change Status of Manipulation Order Templates</value>
  </data>
  <data name="Permissao_ProducaoModeloOrdemManipulacao" xml:space="preserve">
    <value>Model order manipulation</value>
  </data>
  <data name="ReceitaManipuladaItem_DataValidadeNaoEncontrada" xml:space="preserve">
    <value>Unable to identify product expiration date {0}</value>
  </data>
  <data name="ReceitaManipulada_ExcipienteNaoEncontrado" xml:space="preserve">
    <value>Could not find any excipient. Please check your product data.</value>
  </data>
  <data name="ReceitaManipulada_MateriaPrimaNaoEncontrada" xml:space="preserve">
    <value>Could not find any raw materials for recipe calculation.</value>
  </data>
  <data name="NotaFiscalEntradaLote_InformacaoTecnicaObrigatorio" xml:space="preserve">
    <value>Mandatory Technical Information for InventoryItemId {0} as the Product belongs to the Raw Material class</value>
  </data>
  <data name="ProdutoEmbalagem_Invalido" xml:space="preserve">
    <value>The product is not a valid package</value>
  </data>
  <data name="ProdutoTipoCapsula_Invalido" xml:space="preserve">
    <value>The product is not a valid capsule</value>
  </data>
  <data name="ReceitaManipulada_DosagemTipoCalculoInvalido" xml:space="preserve">
    <value>It is not allowed to register the quantity of doses for pharmaceutical forms with the calculation type Recipe.</value>
  </data>
  <data name="Produto_GrupoSemProjecaoEstoque" xml:space="preserve">
    <value>Stock projection is disabled for all Products in Group: {0}.</value>
  </data>
  <data name="ReceitaManipuladaItem_FormaFarmaceuticaInvalida" xml:space="preserve">
    <value>The associated item is not compatible with the prescription's dosage form.</value>
  </data>
  <data name="ProjecaoEstoque_IntervaloInvalido" xml:space="preserve">
    <value>The 'Start' value must be greater than the 'End' value of the projection to form a valid range</value>
  </data>
  <data name="Produto_EstoqueMinimoInvalido" xml:space="preserve">
    <value>The minimum stock cannot be negative</value>
  </data>
  <data name="Produto_EstoqueMaximoInvalido" xml:space="preserve">
    <value>The maximum stock cannot be negative</value>
  </data>
  <data name="Produto_EstoqueInvalido" xml:space="preserve">
    <value>The minimum stock cannot be greater than the maximum</value>
  </data>
  <data name="ModeloOrdemManipulacao_NomeJaExiste" xml:space="preserve">
    <value>There is already a manipulation order template for the chosen type with description {0}</value>
  </data>
  <data name="ModeloOrdemManipulacao_GuidNaoEncontrado" xml:space="preserve">
    <value>Could not find any manipulation order template with guid: {0}</value>
  </data>
  <data name="ProjecaoEstoque_DadosIncompletos" xml:space="preserve">
    <value>Please fill in all inventory days and analysis period fields.</value>
  </data>
  <data name="NecessidadeCompra_FiltroIncorreto" xml:space="preserve">
    <value>The Product filter cannot be combined with other filters.</value>
  </data>
  <data name="Permissao_ProducaoOrdemManipulacaoReceitaManipuladaEmitir" xml:space="preserve">
    <value>Issue manipulation order</value>
  </data>
  <data name="Permissao_ProducaoOrdemManipulacaoReceitaManipuladaVerLista" xml:space="preserve">
    <value>See list of manipulation orders</value>
  </data>
  <data name="Permissao_ProducaoRotuloReceitaManipuladaEditarDetalhes" xml:space="preserve">
    <value>Edit Revenue label</value>
  </data>
  <data name="Permissao_ProducaoRotuloReceitaManipuladaEmitir" xml:space="preserve">
    <value>Issue revenue label</value>
  </data>
  <data name="Permissao_ProducaoRotuloReceitaManipuladaVerLista" xml:space="preserve">
    <value>See list of revenue labels</value>
  </data>
  <data name="Permissao_ProducaoOrdemManipulacaoReceitaManipulada" xml:space="preserve">
    <value>Issuance of Manipulation Order</value>
  </data>
  <data name="Permissao_ProducaoRotuloReceitaManipulada" xml:space="preserve">
    <value>Issuance of Revenue Label</value>
  </data>
  <data name="Permissao_EstoqueProjecaoVisualizar" xml:space="preserve">
    <value>View stock projection</value>
  </data>
  <data name="Permissao_EstoqueProjecao" xml:space="preserve">
    <value>Stock projection</value>
  </data>
  <data name="ReceitaManipulada_StatusInvalido" xml:space="preserve">
    <value>It is not possible to modify a Compounded Prescription with Status {0}</value>
  </data>
  <data name="ProdutoEmbalagem_ModeloRotuloAssociacaoExistenteParaTodasAsFormasFarmaceuticas" xml:space="preserve">
    <value>It is not possible to link two or more Label Templates for all Pharmaceutical Forms.</value>
  </data>
  <data name="ProdutoEmbalagem_ModeloRotuloDuplicadoParaFormaFarmaceutica" xml:space="preserve">
    <value>It is not possible to link two or more Label Templates for the same Pharmaceutical Form.</value>
  </data>
  <data name="ProdutoFichaTecnica_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any Technical Sheet with id: {0}</value>
  </data>
  <data name="Bibliografia_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any Bibliography with id: {0}</value>
  </data>
  <data name="Ensaio_Controle_Qualidade_IdNaoEncontrado" xml:space="preserve">
    <value>Could not find any Quality Control Assay with id: {0}</value>
  </data>
  <data name="Permissao_EstoqueProdutosFichaTecnicaEditar" xml:space="preserve">
    <value>Edit product datasheet</value>
  </data>
  <data name="Permissao_EstoqueProdutosFichaTecnicaVisualizar" xml:space="preserve">
    <value>View product datasheet</value>
  </data>
</root>