using System.Diagnostics;
using Bootis.Compra.Domain.Enumerations;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Events;
using Bootis.Shared.Application.Events;
using Bootis.Shared.Application.Interfaces;
using Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;
using Bootis.Venda.Domain.Enumerations;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bootis.Venda.Application.EventHandlers;

public class ReceitaManipuladaCriadaComPedidoVendaEventHandler(
    IUnitOfWork unitOfWork,
    IPedidoVendaRepository pedidoVendaRepository,
    IReceitaManipuladaRepository receitaManipuladaRepository,
    ILogger<ReceitaManipuladaCriadaComPedidoVendaEventHandler> logger)
    : INotificationHandler<DomainEventNotification<ReceitaManipuladaCriadaComPedidoVendaEvent>>
{
    public async Task Handle(DomainEventNotification<ReceitaManipuladaCriadaComPedidoVendaEvent> notification, 
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();
        var domainEvent = notification.DomainEvent;

        var pedidoVenda = await pedidoVendaRepository.ObterPedidoVendaPorIdAsync(domainEvent.PedidoVendaId);
        var receitaManipulada = await receitaManipuladaRepository.ObterReceitaManipuladaPorIdAsync(
            domainEvent.ReceitaManipuladaId);

        VincularReceitaAoPedidoVenda(pedidoVenda, receitaManipulada);

        unitOfWork.Update(pedidoVenda);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        stopwatch.Stop();
        logger.LogInformation(
            "[PERF] ReceitaManipuladaCriadaComPedidoVendaEventHandler took {ElapsedMs}ms",
            stopwatch.ElapsedMilliseconds);
    }

    private static void VincularReceitaAoPedidoVenda(PedidoVenda pedidoVenda, ReceitaManipulada receitaManipulada)
    {
        const decimal quantidade = 1;

        var descricao =
            $"Receita {receitaManipulada.SequenciaGroupTenant} {receitaManipulada.FormaFarmaceutica.Descricao}" +
            $" {receitaManipulada.QuantidadeReceita} {receitaManipulada.FormaFarmaceutica.Apresentacao}";

        var valorTotalItem = receitaManipulada.Valores.ValorTotal * quantidade;

        pedidoVenda.AdicionarItemReceitaManipulada(receitaManipulada, quantidade, descricao,
            receitaManipulada.Valores.ValorTotal, receitaManipulada.Valores.ValorDesconto,
            valorTotalItem, TipoDesconto.DescontoMonetario, StatusVendaItem.Orcado, true,
            pedidoVenda.Atendimento.AtendenteId);
    }
}