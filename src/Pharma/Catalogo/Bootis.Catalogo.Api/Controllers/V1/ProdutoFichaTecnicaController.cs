using Asp.Versioning;
using Bootis.Catalogo.Application.Requests.ProdutoFichaTecnica.Atualizar;
using Bootis.Catalogo.Application.Requests.ProdutoFichaTecnica.Cadastrar;
using Bootis.Catalogo.Application.Requests.ProdutoFichaTecnica.Obter;
using Bootis.Shared.Api.Attributes;
using Bootis.Shared.Common;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Catalogo.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Catalogo")]
[Route("catalogo/v{version:apiVersion}/[controller]")]
public class ProdutoFichaTecnicaController(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_ProdutosFichaTecnica_Editar)]
    public async Task<IActionResult> Cadastrar([FromBody] CadastrarRequest request)
    { 
        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_ProdutosFichaTecnica_Editar)]
    public async Task<IActionResult> Atualizar([FromBody] AtualizarRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpGet]
    [Route("{produtoId:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Estoque_ProdutosFichaTecnica_Visualizar)]
    public async Task<IActionResult> Obter(Guid produtoId)
    {
        var result = await mediator.Send(new ObterRequest { ProdutoId = produtoId });

        return Ok(result);
    }
}
