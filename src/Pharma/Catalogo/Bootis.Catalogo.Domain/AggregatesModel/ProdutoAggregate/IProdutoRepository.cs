using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;

public interface IProdutoRepository : IRepository<Produto>, IScopedService
{
    Task<Produto> ObterProdutoPorId(Guid id);
    Task<List<Produto>> ObterProdutosComGrupoPorIds(IEnumerable<Guid> ids);
    Task<Produto> ObterPorIdAsync(Guid id);
    Task<Produto> ObterSemInclusoesAsync(Guid id);
    Task<TipoClasseProdutoAbreviacao> ObterClasseProdutoPorIdAsync(Guid produtoId);
    Task<Produto> ObterProdutoComEspecificacoesPorIdAsync(Guid id);
    Task<Produto> ObterComDiluicoesPorIdAsync(Guid id);
    Task<ProdutoMateriaPrima> ObterMateriaPrimaPorIdAsync(Guid id);

    Task<ProdutoEmbalagem> ObterProdutoEmbalagemPorEmbalagemClassificacaoAsync(Guid produtoId,
        Guid? embalagemClassificacaoId);

    Task<List<Produto>> ObterPorFiltrosAsync(List<Guid> grupoIds, List<Guid> subGrupoIds,
        List<Guid> fornecedorIds);

    Task<List<Produto>> ObterProdutosComGrupoAsync(IEnumerable<Guid> produtoIds, bool tracking,
        CancellationToken cancellationToken);

    Task<List<Produto>> ObterProdutosComGrupoAsync(CancellationToken cancellationToken);

    Task<ProdutoMateriaPrima> ObterMateriaPrimaPorId(Guid produtoId);
    Task<Produto> ObterProdutoIncludesExclusaoAsync(Guid id);
    Task<List<Produto>> ObterProdutoPorIds(IEnumerable<Guid> ids);
    Task<List<Produto>> ObterProdutosPorIds(IEnumerable<Guid> ids);
    Task<Produto> ObterProdutoAcabadoPorIdAsync(Guid id);
    Task<List<Produto>> ObterProdutosEmbalagemPorIdsAsync(IEnumerable<Guid> ids);
    Task<List<Produto>> ObterProdutosMateriaPrimaPorIdsAsync(IEnumerable<Guid> ids);
    Task<IEnumerable<(Guid Id, string Descricao)>> VerificarDependenciasProdutosAsync(
        IEnumerable<Guid> ids);
    Task<bool> ValidarPorIdAsync(Guid id);
    Task<bool> ValidarPorDescricaoAsync(string descricao);
    Task<bool> ValidarPorIdEDescricaoAsync(Guid id, string descricao);
    Task<bool> ValidarMovimentacaoAsync(Guid produtoId);
    void Remove(ProdutoMateriaPrima produtoMateriaPrima);
    void Remove(ProdutoEmbalagem produtoEmbalagem);
    void Remove(ProdutoTipoCapsula produtoTipoCapsula);
    void Remove(ProdutoCapsulaPronta produtoCapsulaPronta);
}