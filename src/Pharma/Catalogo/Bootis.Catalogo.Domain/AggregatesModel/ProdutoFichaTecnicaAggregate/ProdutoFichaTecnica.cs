using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.Dtos.ProdutoFichaTecnica;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate;

public class ProdutoFichaTecnica() : Entity, IAggregateRoot
{
    public ProdutoFichaTecnica(Guid produtoId,
                               Guid usuarioId,
                               IProdutoFichaTecnicaDto dto) : this()
    {
        ProdutoId = produtoId;
        UsuarioId = usuarioId;
        NomeCientifico = dto.NomeCientifico;
        NomePopular = dto.NomePopular;
        FormulaMolecular = dto.FormulaMolecular;
        InformacoesComplementares = dto.InformacoesComplementares;
        NumeroRevisao ++;
        DataCadastro = DateOnly.FromDateTime(DateTime.UtcNow);
    }

    public Guid ProdutoId { get; set; }
    public Guid UsuarioId { get; set; }
    public string NomeCientifico { get; set; }
    public string NomePopular { get; set; }
    public string FormulaMolecular { get; set; }
    public string InformacoesComplementares { get; set; }
    public int NumeroRevisao { get; set; }
    public DateOnly DataCadastro { get; set; }

    public void AdicionarEspecificacoes(
        IEnumerable<IProdutoFichaTecnicaEspecificacaoDto> especificacaoDto)
    {
        foreach (var dto in especificacaoDto)
        {
            AdicionarProdutoFichaTecnicaEspecificacao(dto);
        }
    }

    public void AdicionarProdutoFichaTecnicaEspecificacao(IProdutoFichaTecnicaEspecificacaoDto dto)
    {
        var produtoFichaTecnicaEspecificacao = new ProdutoFichaTecnicaEspecificacao(
            this,
            dto.EnsaioId,
            dto.BibliografiaId,
            dto.Especificacao,
            dto.MostrarCertificadoAnalise);

        Especificacoes.Add(produtoFichaTecnicaEspecificacao);
    }

    public void Atualizar(IProdutoFichaTecnicaDto dto, IEnumerable<IProdutoFichaTecnicaEspecificacaoDto> especificacoesDto)
    {
        NomeCientifico = dto.NomeCientifico;
        NomePopular = dto.NomePopular;
        FormulaMolecular = dto.FormulaMolecular;
        InformacoesComplementares = dto.InformacoesComplementares;
        NumeroRevisao++;

        var especificacoes = Especificacoes.ToList();

        if(especificacoesDto.Any())
        {
            foreach(var especificacaoDto in especificacoesDto)
            {
                var especificacao = especificacoes
                    .SingleOrDefault(c => c.EnsaioControleQualidadeId == especificacaoDto.EnsaioId && 
                                     c.BibliografiaId == especificacaoDto.BibliografiaId);

                if(especificacao is null)
                {
                    AdicionarProdutoFichaTecnicaEspecificacao(especificacaoDto);

                    continue;
                }

                especificacao.AtualizarEspecificacao(especificacaoDto);
                especificacoes.Remove(especificacao);
            }
        }

        foreach(var especificacao in especificacoes)
        {
            Especificacoes.Remove(especificacao);
        }
    }


    #region Navigation Properties

    public virtual Produto Produto { get; set; }
    public virtual Usuario Usuario { get; set; }
    public virtual ICollection<ProdutoFichaTecnicaEspecificacao> Especificacoes { get; set; } = new List<ProdutoFichaTecnicaEspecificacao>();

    #endregion
}
