using Bootis.Catalogo.Domain.AggregatesModel.BibliografiaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.EnsaioControleQualidadeAggregate;
using Bootis.Catalogo.Domain.Dtos.ProdutoFichaTecnica;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoFichaTecnicaAggregate;

public class ProdutoFichaTecnicaEspecificacao()
{
    public ProdutoFichaTecnicaEspecificacao(ProdutoFichaTecnica produtoFichaTecnica,
                                            Guid ensaioId,
                                            Guid bibliografiaId,
                                            string especificacao,
                                            bool mostrarCertificadoAnalise) : this()
    {
        ProdutoFichaTecnica = produtoFichaTecnica;
        EnsaioControleQualidadeId = ensaioId;
        BibliografiaId = bibliografiaId;
        Especificacao = especificacao;
        MostrarCertificadoAnalise = mostrarCertificadoAnalise;
    }

    public Guid ProdutoFichaTecnicaId { get; set; }
    public Guid EnsaioControleQualidadeId { get; set; }
    public Guid BibliografiaId { get; set; }
    public string Especificacao { get; set; }
    public bool MostrarCertificadoAnalise { get; set; }

    public void AtualizarEspecificacao(IProdutoFichaTecnicaEspecificacaoDto dto)
    {
        EnsaioControleQualidadeId = dto.EnsaioId;
        BibliografiaId = dto.BibliografiaId;
        Especificacao = dto.Especificacao;
        MostrarCertificadoAnalise = dto.MostrarCertificadoAnalise;
    }

    #region Navigation Properties

    public virtual ProdutoFichaTecnica ProdutoFichaTecnica { get; set; }
    public virtual EnsaioControleQualidade EnsaioControleQualidade { get; set; }
    public virtual Bibliografia Bibliografia { get; set; }

    #endregion
}
