using Bootis.Catalogo.Application.Requests.Produto.Remover;
using Bootis.Catalogo.Common;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoCapsulaPronta;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoEmbalagem;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Catalogo.Application.Extensions;

public static class ProdutoRepositoryExtension
{
    #region Consultas

    public static async Task<Produto> ObterProdutoAsync(this IProdutoRepository repository, Guid id)
    {
        var produto = await repository.ObterProdutoPorId(id).ConfigureAwait(false);

        if (produto is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Produto_GuidNaoEncontrado(id));

        return produto;
    }

    public static async Task<Produto> ObterProdutoSemInclusoesAsync(this IProdutoRepository repository, Guid id)
    {
        var produto = await repository.ObterSemInclusoesAsync(id);

        if (produto is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Produto_GuidNaoEncontrado(id));

        return produto;
    }

    public static async Task<Produto> ObterProdutoComEspecificacoesAsync(this IProdutoRepository repository,
        Guid id)
    {
        var produto = await repository.ObterProdutoComEspecificacoesPorIdAsync(id)
            .ConfigureAwait(false);

        if (produto is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Produto_GuidNaoEncontrado(id));

        return produto;
    }

    public static async Task<Produto> ObterProdutoComDiluicoesAsync(this IProdutoRepository repository, Guid id)
    {
        var produto = await repository.ObterComDiluicoesPorIdAsync(id);

        if (produto is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Produto_GuidNaoEncontrado(id));

        return produto;
    }

    public static async Task<Produto> ObterProdutoPorIdAsync(this IProdutoRepository repository, Guid id)
    {
        var produto = await repository.ObterPorIdAsync(id);

        if (produto is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Produto_IdNaoEncontrado(id));

        return produto;
    }

    public static async Task<Produto> ObterProdutoAcabadoAsync(this IProdutoRepository repository, Guid id)
    {
        var produtoAcabado = await repository.ObterProdutoAcabadoPorIdAsync(id);

        if (produtoAcabado is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Produto_GuidNaoEncontrado(id));

        return produtoAcabado;
    }

    public static async Task<Produto> ObterProdutoComExclusaoAsync(this IProdutoRepository repository, Guid id)
    {
        var produto = await repository.ObterProdutoIncludesExclusaoAsync(id).ConfigureAwait(false);

        if (produto is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Produto_GuidNaoEncontrado(id));

        return produto;
    }

    public static async Task<TipoClasseProdutoAbreviacao> ObterClasseProdutoAsync(this IProdutoRepository repository,
        Guid id)
    {
        var classeProdutoId = await repository.ObterClasseProdutoPorIdAsync(id);

        if (!Enum.IsDefined(typeof(TipoClasseProdutoAbreviacao), classeProdutoId))
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Produto_GuidNaoEncontrado(id));

        return classeProdutoId;
    }

    public static async Task<List<Produto>> ObterProdutosAsync(this IProdutoRepository repository,
        IEnumerable<Guid> ids)
    {
        var produtos = await repository.ObterProdutoPorIds(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !produtos.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_Produto_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos),
                idsInvalidos);

        return produtos;
    }

    public static async Task<List<Produto>> ObterProdutosIncludeAsync(this IProdutoRepository repository,
        IEnumerable<Guid> ids)
    {
        var produtos = await repository.ObterProdutosPorIds(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !produtos.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_Produto_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos),
                idsInvalidos);

        return produtos;
    }

    public static async Task<IEnumerable<ProdutoEmbalagem>> ObterProdutosEmbalagemAsync(
        this IProdutoRepository repository,
        IEnumerable<Guid> ids)
    {
        var produtos = await repository.ObterProdutosEmbalagemPorIdsAsync(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !produtos.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_ProdutoEmbalagem_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(
                nameof(ProdutoEmbalagemAssociacaoDto.ProdutoEmbalagemId),
                idsInvalidos);

        return produtos?.Select(c => c.ProdutoEmbalagem);
    }

    public static async Task<IEnumerable<ProdutoMateriaPrima>> ObterProdutosMateriaPrimaAsync(
        this IProdutoRepository repository,
        IEnumerable<Guid> ids)
    {
        var produtos = await repository.ObterProdutosMateriaPrimaPorIdsAsync(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !produtos.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_ProdutoMateriaPrima_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(
                nameof(ProdutoCapsulaProntaMateriaPrimaAssociacaoDto.ProdutoMateriaPrimaId),
                idsInvalidos);

        return produtos?.Select(c => c.ProdutoMateriaPrima);
    }

    public static async Task<ProdutoMateriaPrima> ObterProdutoExcipienteAsync(this IProdutoRepository repository,
        Guid? id)
    {
        if (id.HasValue)
        {
            var produtoExcipiente =
                await repository.ObterMateriaPrimaPorIdAsync(id.Value).ConfigureAwait(false);

            if (produtoExcipiente is null)
                throw new ValidationException(nameof(id.Value),
                    Localizer.Instance.GetMessage_ProdutoMateriaPrima_GuidNaoEncontrado(id.Value));

            if (!produtoExcipiente.IsExcipiente.GetValueOrDefault())
                throw new ValidationException(nameof(id.Value),
                    Localizer.Instance.GetMessage_ProdutoMateriaPrima_NaoEhExcipiente());

            return produtoExcipiente;
        }

        return null;
    }

    #endregion

    #region Validações

    public static async Task ValidarProdutoPorIdAsync(this IProdutoRepository repository, Guid id)
    {
        if (!await repository.ValidarPorIdAsync(id))
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_Produto_IdNaoEncontrado(id));
    }

    public static async Task ValidarProdutoPorDescricaoAsync(this IProdutoRepository repository, string descricao)
    {
        var hasDescricao = await repository.ValidarPorDescricaoAsync(descricao)
            .ConfigureAwait(false);

        if (hasDescricao)
            throw new ValidationException(nameof(descricao),
                Localizer.Instance.GetMessage_Produto_DescricaoExistente(descricao));
    }

    public static async Task ValidarProdutoPorIdEDescricaoAsync(this IProdutoRepository repository,
        Guid id, string descricao)
    {
        var hasDescricao = await repository.ValidarPorIdEDescricaoAsync(id, descricao)
            .ConfigureAwait(false);

        if (!hasDescricao)
            await ValidarProdutoPorDescricaoAsync(repository, descricao);
    }


    public static async Task ValidarProdutoEmbalagemPorEmbalagemClassificacaoAsync(this IProdutoRepository repository,
        Produto produto, Guid? embalagemClassificacaoId)
    {
        var produtoEmbalagem = await repository
            .ObterProdutoEmbalagemPorEmbalagemClassificacaoAsync(produto.Id, embalagemClassificacaoId);

        if (produtoEmbalagem is not null && produto.Id != produtoEmbalagem.Produto.Id)
            throw new ValidationException(nameof(produtoEmbalagem.Produto.Id),
                Localizer.Instance.GetMessage_ProdutoEmbalagem_JaVinculado(produtoEmbalagem.Produto.Id));
    }

    public static async Task ValidarMovimentacaoProdutoAsync(this IProdutoRepository repository, Guid id,
        string produtoDescricao)
    {
        if (await repository.ValidarMovimentacaoAsync(id))
            throw new DomainException(nameof(CatalogoErrorCode), (int)CatalogoErrorCode.Produto_ValidationMovimentacao,
                produtoDescricao);
    }

    public static async Task VerificarDependenciasAsync(this IProdutoRepository repository, List<Produto> produtos)
    {
        var removeResponse = new List<RemoverResponse>();
        var ids = produtos.Select(l => l.Id).ToList();
        var produtosDependentes = await repository.VerificarDependenciasProdutosAsync(ids);

        removeResponse.AddRange(produtosDependentes.Select(x => new RemoverResponse
        {
            Descricao = x.Descricao,
            ProdutoId = x.Id
        }));

        if (removeResponse.Count > 0)
            throw new DomainException(nameof(CatalogoErrorCode), (int)CatalogoErrorCode.Produto_Remover,
                removeResponse.ToArray());
    }

    #endregion
}