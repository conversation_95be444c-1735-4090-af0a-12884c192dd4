using Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Catalogo.Infrastructure.Repositories;

public class ProdutoIncompativelRepository(IUserContext userContext, IDbContext context)
    : Repository<ProdutoIncompativel>(context), IProdutoIncompativelRepository
{
    public Task<ProdutoIncompativel> ObterPorIdAsync(Guid id)
    {
        return DbSet
            .SingleOrDefaultAsync(c => c.Id == id && c.GroupTenantId == userContext.GroupTenantId);
    }

    public async Task<IEnumerable<ProdutoIncompativel>> ObterIncompatibilidadesEntreAsync(IEnumerable<Guid> produtoIds, NivelIncompatibilidade nivel)
    {
        var listaIds = produtoIds.ToList();

        return await DbSet
            .Include(pi => pi.Produto)
            .Include(pi => pi.ProdutoIncompatibilidade)
            .Where(pi => pi.GroupTenantId == userContext.GroupTenantId &&
                        pi.NivelIncompatibilidade == nivel &&
                        (listaIds.Contains(pi.ProdutoId) && listaIds.Contains(pi.ProdutoIncompativelId)))
            .ToListAsync();
    }

    public async Task<IEnumerable<ProdutoIncompativel>> ObterTodasIncompatibilidadesEntreAsync(IEnumerable<Guid> produtoIds)
    {
        var listaIds = produtoIds.ToList();

        return await DbSet
            .Include(pi => pi.Produto)
            .Include(pi => pi.ProdutoIncompatibilidade)
            .Where(pi => pi.GroupTenantId == userContext.GroupTenantId &&
                        (listaIds.Contains(pi.ProdutoId) && listaIds.Contains(pi.ProdutoIncompativelId)))
            .ToListAsync();
    }

    public async Task<bool> ValidarVinculoPorProdutoAsync(Guid produtoId, Guid produtoIncompativelId)
    {
        const string sql = """
                            SELECT CASE 
                                      WHEN EXISTS (SELECT 1 
                                                    FROM produtos_incompativel pi
                                                    WHERE pi.produto_id = @produtoId
                                                      AND pi.produto_incompativel_id = @produtoIncompativelId
                                                      AND pi.group_tenant_id = @groupTenantId) 
                                      THEN 1 
                                      ELSE 0 
                                  END AS existe_registro;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { produtoId, produtoIncompativelId, groupTenantId = userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<bool> ValidarVinculoPorIdEProdutoAsync(Guid id, Guid produtoId,
        Guid produtoIncompativelId)
    {
        const string sql = """
                           SELECT CASE 
                                      WHEN EXISTS (SELECT 1 
                                                    FROM produtos_incompativel pi
                                                    WHERE pi.id = @id
                                                      AND pi.produto_id = @produtoId
                                                      AND pi.produto_incompativel_id = @produtoIncompativelId
                                                      AND pi.group_tenant_id = @groupTenantId) 
                                      THEN 1 
                                      ELSE 0 
                                  END AS existe_registro;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new
            {
                id, produtoId, produtoIncompativelId, groupTenantId = userContext.GroupTenantId
            },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }
}