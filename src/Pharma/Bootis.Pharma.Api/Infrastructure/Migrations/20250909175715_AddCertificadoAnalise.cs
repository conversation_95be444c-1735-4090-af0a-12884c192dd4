using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bootis.Pharma.Api.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddCertificadoAnalise : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "certificados_analise",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    sequencia_group_tenant = table.Column<int>(type: "integer", nullable: false),
                    data_emissao = table.Column<DateOnly>(type: "date", nullable: false),
                    lote_id = table.Column<Guid>(type: "uuid", nullable: false),
                    quantidade_amostragem = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: false),
                    unidade_amostragem_id = table.Column<int>(type: "integer", nullable: false),
                    informacoes_complementares = table.Column<string>(type: "character varying(5000)", maxLength: 5000, nullable: true),
                    aprovado = table.Column<bool>(type: "boolean", nullable: false),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    is_removed = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_certificados_analise", x => x.id);
                    table.ForeignKey(
                        name: "fk_certificados_analise_lotes_lote_id",
                        column: x => x.lote_id,
                        principalTable: "lotes",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "certificados_analise_especificacao",
                columns: table => new
                {
                    certificado_analise_id = table.Column<Guid>(type: "uuid", nullable: false),
                    ensaio_controle_qualidade_id = table.Column<Guid>(type: "uuid", nullable: false),
                    especificacoes = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    resultado = table.Column<string>(type: "character varying(300)", maxLength: 300, nullable: false),
                    bibliografia_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_certificados_analise_especificacao", x => new { x.certificado_analise_id, x.ensaio_controle_qualidade_id });
                    table.ForeignKey(
                        name: "fk_certificados_analise_especificacao_bibliografias_bibliograf",
                        column: x => x.bibliografia_id,
                        principalTable: "bibliografias",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "fk_certificados_analise_especificacao_certificados_analise_cer",
                        column: x => x.certificado_analise_id,
                        principalTable: "certificados_analise",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_certificados_analise_especificacao_ensaios_controle_qualida",
                        column: x => x.ensaio_controle_qualidade_id,
                        principalTable: "ensaios_controle_qualidade",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "ix_certificados_analise_group_tenant_id",
                table: "certificados_analise",
                column: "group_tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_certificados_analise_lote_id",
                table: "certificados_analise",
                column: "lote_id");

            migrationBuilder.CreateIndex(
                name: "ix_certificados_analise_tenant_id",
                table: "certificados_analise",
                column: "tenant_id");

            migrationBuilder.CreateIndex(
                name: "ix_certificados_analise_especificacao_bibliografia_id",
                table: "certificados_analise_especificacao",
                column: "bibliografia_id");

            migrationBuilder.CreateIndex(
                name: "ix_certificados_analise_especificacao_ensaio_controle_qualidad",
                table: "certificados_analise_especificacao",
                column: "ensaio_controle_qualidade_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "certificados_analise_especificacao");

            migrationBuilder.DropTable(
                name: "certificados_analise");
        }
    }
}
