using Bootis.Producao.Domain.AggregatesModel.CertificadoAnaliseAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Bootis.Shared.Infrastructure.ValueGenerators;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Producao.Infrastructure.EntityConfigurations;

public class CertificadoAnaliseEntityTypeConfiguration : BaseEntityTypeConfiguration<CertificadoAnalise>
{
    public override void Configure(EntityTypeBuilder<CertificadoAnalise> builder)
    {
        builder.ToTable("certificados_analise");
        
        builder
            .Property(c => c.SequenciaGroupTenant)
            .HasValueGenerator<GroupTenantSequenceValueGenerator>();

        builder
            .Property(c => c.DataEmissao)
            .Data()
            .IsRequired();

        builder
            .HasOne(c => c.Lote)
            .WithMany()
            .HasForeignKey(c => c.LoteId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.QuantidadeAmostragem)
            .Estoque()
            .IsRequired();

        builder
            .Property(c => c.UnidadeAmostragemId)
            .IsRequired();

        builder
            .Property(c => c.InformacoesComplementares)
            .Observacao(TamanhoTexto.CincoMil)
            .IsRequired(false);

        builder
            .Property(c => c.Aprovado)
            .IsRequired();

        base.Configure(builder);
    }
}
