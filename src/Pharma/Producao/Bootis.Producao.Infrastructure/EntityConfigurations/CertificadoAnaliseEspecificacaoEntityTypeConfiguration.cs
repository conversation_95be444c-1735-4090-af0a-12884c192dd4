using Bootis.Producao.Domain.AggregatesModel.CertificadoAnaliseAggregate;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Producao.Infrastructure.EntityConfigurations;

public class CertificadoAnaliseEspecificacaoEntityTypeConfiguration : IEntityTypeConfiguration<CertificadoAnaliseEspecificacao>
{

    public void Configure(EntityTypeBuilder<CertificadoAnaliseEspecificacao> builder)
    {
        builder.ToTable("certificados_analise_especificacao")
            .HasKey(c => new { c.CertificadoAnaliseId, c.EnsaioControleQualidadeId });

        builder
            .HasOne(c => c.CertificadoAnalise)
            .WithMany(c => c.Especificacoes)
            .HasForeignKey(c => c.CertificadoAnaliseId)
            .OnDelete(DeleteBehavior.Cascade)
            .IsRequired();

        builder
            .HasOne(c => c.EnsaioControleQualidade)
            .WithMany()
            .HasForeignKey(c => c.EnsaioControleQualidadeId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder
            .Property(c => c.Especificacoes)
            .Observacao(TamanhoTexto.CentoECinquenta)
            .IsRequired();

        builder
            .Property(c => c.Resultado)
            .Observacao(Shared.Infrastructure.Enums.TamanhoTexto.Trezentos)
            .IsRequired();
    }
}
