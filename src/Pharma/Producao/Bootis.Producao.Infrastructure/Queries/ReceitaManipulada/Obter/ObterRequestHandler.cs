using System.Data;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.ReceitaManipulada.Obter;

public class ObterRequestHandler(
    IDbConnection connection) : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT rm.id,
                                  cl.id AS paciente_id,
                                  cl.nome AS paciente_descricao,
                                  pr.id AS prescritor_id,
                                  pr.nome_completo AS prescritor_descricao,
                                  ff.id AS forma_farmaceutica_id,
                                  ff.descricao AS forma_farmaceutica_descricao,
                                  ff.apresentacao AS forma_farmaceutica_apresentacao,
                                  ff.tipo_calculo AS forma_farmaceutica_tipo_calculo,
                                  rm.quantidade_receita,
                                  rm.quantidade_repetir,
                                  rm.quantidade_dose,
                                  rm.uso_continuo,
                                  rm.tipo_uso_continuo,
                                  rm.quantidade_re_sell AS quantidade_resell,
                                  rm.periodicidade_re_sell AS periodicidade_resell,
                                  po.id AS posologia_id,
                                  po.descricao AS posologia_descricao,
                                  rm.data_emissao,
                                  rm.data_prescricao,
                                  rm.data_validade_prescricao AS data_validade_receita,
                                  rm.observacao,
                                  rm.previsao_entrega,
                                  rv.valor_bruto,
                                  rv.valor_desconto AS desconto,
                                  rv.valor_total
                             FROM receitas_manipuladas rm
                                  LEFT JOIN clientes cl ON cl.id = rm.paciente_id
                                  LEFT JOIN prescritores pr ON pr.id = rm.prescritor_id
                                  LEFT JOIN formas_farmaceutica ff ON ff.id = rm.forma_farmaceutica_id
                                  LEFT JOIN posologias po ON po.id = rm.posologia_id
                                  LEFT JOIN receitas_manipuladas_valores rv ON rv.receita_manipulada_id = rm.id
                            WHERE rm.id = @id
                           """;

        const string sqlCapsula = """
                                  SELECT p.id AS produto_capsula_id,
                                         p.descricao AS produto_capsula_descricao,
                                         rm.quantidade_receita AS quantidade_capsula
                                    FROM receitas_manipuladas rm
                                         LEFT JOIN receita_manipulada_rastreio_calculos rr ON rr.receita_manipulada_id = rm.id
                                         LEFT JOIN produtos_tipo_capsula ptc ON ptc.produto_id = rr.produto_id AND rr.tipo_origem = 5
                                         LEFT JOIN produtos p ON p.id = ptc.produto_id
                                   WHERE rm.id = @id
                                     AND rr.tipo_origem = 5
                                  """;

        const string sqlEmbalagem = """
                                    SELECT p.id AS produto_embalagem_id,
                                           p.descricao AS produto_embalagem_descricao,
                                           1 AS quantidade_embalagem
                                      FROM receitas_manipuladas rm
                                           LEFT JOIN receita_manipulada_rastreio_calculos rr ON rr.receita_manipulada_id = rm.id
                                           LEFT JOIN produtos_embalagem emb ON emb.produto_id = rr.produto_id AND rr.tipo_origem = 6
                                           LEFT JOIN produtos p ON p.id = emb.produto_id
                                     WHERE rm.id = @id
                                       AND rr.tipo_origem = 6
                                    """;

        const string sqlComponentes = """
                                          SELECT ri.id AS componente_id,
                                                 CASE 
                                                     WHEN rmis.id IS NOT NULL THEN ps.sinonimo 
                                                     ELSE p.descricao 
                                                 END AS descricao_produto,
                                                 p.descricao AS descricao_produto_original,
                                                 ri.descricao_produto AS descricao_rotulo,
                                                 p.id AS produto_id,
                                                 ri.quantidade,
                                                 ri.unidade_medida_id,
                                                 un.abreviacao AS unidade_medida_abreviacao,
                                                 p.classe_produto_id AS classe_produto,
                                                 fp.id AS formula_padrao_id,
                                                 COALESCE(rmis.produto_sinonimo_id, ps_default.id) AS sinonimo_id,
                                                 ri.ordem,
                                                 ri.oculta_rotulo,
                                                 ri.tipo AS tipo_quantificacao
                                            FROM receitas_manipuladas_item ri
                                                 LEFT JOIN receitas_manipuladas rm ON rm.id = ri.receita_manipulada_id
                                                 LEFT JOIN unidades_medida un ON un.id = ri.unidade_medida_id
                                                 LEFT JOIN produtos p ON p.id = ri.produto_id
                                                 LEFT JOIN formulas_padrao fp ON fp.produto_id = p.id
                                                 LEFT JOIN receitas_manipuladas_item_sinonimo rmis ON rmis.id = ri.id
                                                 LEFT JOIN produtos_sinonimo ps ON ps.id = rmis.produto_sinonimo_id
                                                 LEFT JOIN LATERAL (
                                                     SELECT ps2.id
                                                     FROM produtos_sinonimo ps2 
                                                     WHERE ps2.produto_id = p.id 
                                                     ORDER BY ps2.id 
                                                     LIMIT 1
                                                 ) ps_default ON true
                                           WHERE rm.id = @id
                                            ORDER BY ri.ordem;
                                      """;

        var result = await connection.QuerySingleOrDefaultAsync<dynamic>(sql,
            new { id = request.Id });

        if (result is null)
        {
            var message = Localizer.Instance.GetMessage_ReceitaManipulada_GuidNaoEncontrado(request.Id);
            throw new DomainException(message);
        }

        var capsula = await connection.QueryFirstOrDefaultAsync<ObterResponse>(sqlCapsula,
            new { id = request.Id });

        var embalagem = await connection.QueryFirstOrDefaultAsync<ObterResponse>(sqlEmbalagem,
            new { id = request.Id });

        var componentes = await connection.QueryAsync<ObterComponentesResponse>(sqlComponentes,
            new { id = request.Id });

        var response = new ObterResponse
        {
            Id = result.id,
            PacienteId = result.paciente_id,
            PacienteDescricao = result.paciente_descricao,
            PrescritorId = result.prescritor_id,
            PrescritorDescricao = result.prescritor_descricao,
            FormaFarmaceuticaId = result.forma_farmaceutica_id,
            FormaFarmaceuticaTipoCalculo = (TipoCalculo)result.forma_farmaceutica_tipo_calculo,
            FormaFarmaceuticaDescricao = result.forma_farmaceutica_descricao,
            FormaFarmaceuticaApresentacao = result.forma_farmaceutica_apresentacao,
            QuantidadeDose = result.quantidade_dose,
            QuantidadeReceita = result.quantidade_receita,
            QuantidadeRepetir = result.quantidade_repetir,

            UsoContinuo = result.uso_continuo,
            TipoUsoContinuo = (TipoCalculoUsoContinuo?)result.tipo_uso_continuo,
            QuantidadeReSell = result.quantidade_resell,
            PeriodicidadeReSell = (PeriodosPosologia?)result.periodicidade_resell,

            PosologiaId = result.posologia_id,
            PosologiaDescricao = result.posologia_descricao,
            Observacao = result.observacao,

            ProdutoCapsulaId = capsula?.ProdutoCapsulaId,
            ProdutoCapsulaDescricao = capsula?.ProdutoCapsulaDescricao,
            QuantidadeCapsula = capsula?.QuantidadeCapsula,

            ProdutoEmbalagemId = embalagem?.ProdutoEmbalagemId,
            ProdutoEmbalagemDescricao = embalagem?.ProdutoEmbalagemDescricao,
            QuantidadeEmbalagem = embalagem?.QuantidadeEmbalagem,

            InformacoesAdicionais = new ObterInformacoesAdicionais
            {
                DataEmissao = result.data_emissao,
                DataPrescricao = result.data_prescricao,
                DataValidadeReceita = result.data_validade_receita != null
                    ? result.data_validade_receita
                    : null
            },

            Valores = new ObterValores
            {
                PrevisaoEntrega = result.previsao_entrega,
                ValorReceita = result.valor_bruto,
                Desconto = result.desconto,
                ValorTotal = result.valor_total
            },

            Componentes = componentes
        };

        return response;
    }
}