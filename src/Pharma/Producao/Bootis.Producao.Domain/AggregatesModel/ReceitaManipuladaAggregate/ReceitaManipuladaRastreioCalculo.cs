using System.ComponentModel.DataAnnotations.Schema;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;

public class ReceitaManipuladaRastreioCalculo : Entity
{
    public ReceitaManipuladaRastreioCalculo()
    {
    }

    public ReceitaManipuladaRastreioCalculo(Produto produto, ReceitaManipuladaItem receitaManipuladaItem,
        TipoOrigem tipoOrigem,
        ReceitaManipuladaRastreioCalculo rastreioCalculoOrigem, Guid? loteId, decimal quantidade, int unidadeMedidaId,
        decimal quantidadeVolume, int unidadeMedidaVolumeId, bool geraCalculo, FatoresTotais fatores,
        bool? formulaDesmembra, FormulaPadraoDesmembramento? formulaDesmembramento)
    {
        Produto = produto;
        ReceitaManipuladaItem = receitaManipuladaItem;
        TipoOrigem = tipoOrigem;
        ReceitaManipuladaRastreioCalculoOrigem = rastreioCalculoOrigem;
        LoteId = loteId;
        Quantidade = quantidade;
        UnidadeMedidaId = unidadeMedidaId;
        QuantidadeVolume = quantidadeVolume;
        UnidadeMedidaVolumeId = unidadeMedidaVolumeId;
        GeraCalculo = geraCalculo;
        Fatores = fatores;
        FormulaDesmembra = formulaDesmembra;
        TipoDesmembramento = formulaDesmembramento;
    }

    public Guid ProdutoId { get; set; }
    public virtual Produto Produto { get; set; }
    public Guid? ReceitaManipuladaItemId { get; set; }
    public virtual ReceitaManipuladaItem ReceitaManipuladaItem { get; set; }
    public Guid? ReceitaManipuladaRastreioCalculoId { get; set; }
    public virtual ReceitaManipuladaRastreioCalculo ReceitaManipuladaRastreioCalculoOrigem { get; set; }
    public Guid ReceitaManipuladaId { get; set; }
    public virtual ReceitaManipulada ReceitaManipulada { get; set; }
    public TipoOrigem TipoOrigem { get; private set; }
    public Guid? LoteId { get; private set; }
    public virtual Lote Lote { get; private set; }
    public decimal Quantidade { get; private set; }
    public int UnidadeMedidaId { get; private set; }
    public decimal? QuantidadeVolume { get; private set; }
    public int? UnidadeMedidaVolumeId { get; private set; }
    public bool GeraCalculo { get; private set; }
    public bool? FormulaDesmembra { get; private set; }
    public FormulaPadraoDesmembramento? TipoDesmembramento { get; private set; }

    public bool EhSinonimo => ReceitaManipuladaItem is ReceitaManipuladaItemSinonimo;

    public string ProdutoDescricaoOriginal => Produto?.Descricao;

    public bool EhEditadoNoRotulo
    {
        get
        {
            var descricaoEditada = ReceitaManipuladaItem?.DescricaoProduto?.Trim();
            var descricaoOriginal = Produto?.Descricao?.Trim();
            var descricaoSinonimo =
                (ReceitaManipuladaItem as ReceitaManipuladaItemSinonimo)?.ProdutoSinonimo?.Sinonimo?.Trim();

            if (string.IsNullOrWhiteSpace(descricaoEditada))
                return false;

            return !string.Equals(descricaoEditada, descricaoOriginal, StringComparison.OrdinalIgnoreCase)
                   && !string.Equals(descricaoEditada, descricaoSinonimo, StringComparison.OrdinalIgnoreCase);
        }
    }

    public string ProdutoDescricaoEditada => ReceitaManipuladaItem?.DescricaoProduto;

    public string ProdutoSinonimoDescricao => EhSinonimo
        ? (ReceitaManipuladaItem as ReceitaManipuladaItemSinonimo)?.ProdutoSinonimo?.Sinonimo
        : null;

    public Guid? ProdutoOrigemId => ReceitaManipuladaRastreioCalculoOrigem?.Produto?.Id;

    public Guid? ProdutoSinonimoId => EhSinonimo
        ? (ReceitaManipuladaItem as ReceitaManipuladaItemSinonimo)?.ProdutoSinonimo?.Id
        : null;

    public TipoComponente? TipoComponente => ReceitaManipuladaItem?.Tipo;

    [NotMapped] public bool EhFormula { get; internal set; }

    [NotMapped] public FatoresTotais Fatores { get; private set; }

    [NotMapped] public ProdutosAssociados? Associados { get; internal set; }

    public void AtualizarQuantidade(decimal quantidade)
    {
        Quantidade = quantidade;
    }

    public void Atualizar(Produto novoProduto, decimal novaQuantidade, UnidadeMedidaAbreviacao unidade)
    {
        Produto = novoProduto;
        ProdutoId = novoProduto.Id;
        Quantidade = novaQuantidade;
        UnidadeMedidaId = (int)unidade;
        QuantidadeVolume = unidade == UnidadeMedidaAbreviacao.mL ? novaQuantidade : null;
        UnidadeMedidaVolumeId = unidade == UnidadeMedidaAbreviacao.mL ? unidade.ToInt() : null;
    }

    public void AtualizarQuantidadeVolume(decimal quantidadeVolume, UnidadeMedidaAbreviacao unidadeMedidaVolume)
    {
        QuantidadeVolume = quantidadeVolume;
        UnidadeMedidaVolumeId = unidadeMedidaVolume.ToInt();
    }

    public record struct FatoresTotais(
        decimal FatorTotal = 1,
        decimal FatorEquivalencia = 1,
        decimal FatorFornecedor = 1,
        decimal FatorDiluicaoInterna = 1,
        decimal Densidade = 1,
        decimal FatorConcentracaoAgua = 1,
        decimal? FatorSinonimo = null,
        decimal? FatorCorrecao = null);

    public record struct ProdutosAssociados(
        bool? Acumula,
        decimal? DosagemMaxima,
        decimal? DosagemMinima,
        int? UnidadeMedidaDosagemId
    );
}