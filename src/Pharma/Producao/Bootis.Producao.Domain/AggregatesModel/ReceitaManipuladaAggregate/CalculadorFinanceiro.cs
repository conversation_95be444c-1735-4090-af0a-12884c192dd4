using System;

namespace Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;

public static class CalculadorFinanceiro
{
    public static (decimal valorCusto, decimal valorVenda) CalcularValoresItem(
        ReceitaManipuladaRastreioCalculo rastreio, decimal quantidade)
    {
        var custo = quantidade * rastreio.Produto.ValorCusto;
        var venda = custo + custo * rastreio.Produto.MargemLucro / 100m;

        return (ArredondarValor(custo), ArredondarValor(venda));
    }

    public static void AtualizarValoresAcumulados(ValoresFinanceiros acumulador,
        ReceitaManipuladaRastreioCalculo rastreio,
        decimal quantidade)
    {
        var (valorCustoItem, valorVendaItem) = CalcularValoresItem(rastreio, quantidade);

        acumulador.ValorCustoTotal += valorCustoItem;
        acumulador.ValorVendaTotal += valorVendaItem;
        acumulador.ValorLucroTotal += valorVendaItem - valorCustoItem;
    }

    private static decimal ArredondarValor(decimal valor) =>
        Math.Round(valor, 2, MidpointRounding.AwayFromZero);
}

public sealed class ValoresFinanceiros
{
    public decimal ValorCustoTotal { get; set; }
    public decimal ValorVendaTotal { get; set; }
    public decimal ValorLucroTotal { get; set; }
}