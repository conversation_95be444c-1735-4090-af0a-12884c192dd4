using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;

public class ReceitaManipuladaItem() : Entity
{
    public ReceitaManipuladaItem(ReceitaManipulada receitaManipulada,
        Produto produto,
        string descricaoProduto,
        decimal quantidade,
        int unidadeMedidaId,
        TipoComponente tipo,
        int ordem,
        bool ocultaRotulo) : this()
    {
        ReceitaManipulada = receitaManipulada;
        Produto = produto;
        DescricaoProduto = descricaoProduto;
        Quantidade = quantidade;
        UnidadeMedidaId = unidadeMedidaId;
        Tipo = tipo;
        Ordem = ordem;
        OcultaRotulo = ocultaRotulo;
    }

    public Guid ReceitaManipuladaId { get; private set; }
    public virtual ReceitaManipulada ReceitaManipulada { get; private set; }
    public Guid ProdutoId { get; private set; }
    public virtual Produto Produto { get; private set; }
    public int Ordem { get; private set; }
    public string DescricaoProduto { get; private set; }
    public decimal Quantidade { get; private set; }
    public int UnidadeMedidaId { get; private set; }
    public TipoComponente Tipo { get; private set; }
    public bool OcultaRotulo { get; private set; }

    public void OcultarRotulo(bool oculta)
    {
        OcultaRotulo = oculta;
    }

    public void AtualizarProduto(Produto novoProduto, decimal novaQuantidade, int unidade)
    {
        Produto = novoProduto;
        ProdutoId = novoProduto.Id;
        Quantidade = novaQuantidade;
        UnidadeMedidaId = unidade;
    }

    public void AtualizarOrdem(int novaOrdem)
    {
        Ordem = novaOrdem;
    }

    public virtual bool IsSinonimo()
    {
        return false;
    }
}