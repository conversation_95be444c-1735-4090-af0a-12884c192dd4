using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.CertificadoAnaliseAggregate;

public class CertificadoAnalise() : Entity, IAggregateRoot, ITenant
{
    public CertificadoAnalise(Guid loteId,
                              decimal quantidadeAmostragem,
                              UnidadeMedidaAbreviacao unidadeAmostragemId,
                              string informacoesComplementares,
                              bool aprovado) : this()
    {
        DataEmissao = DateOnly.FromDateTime(DateTime.UtcNow);
        LoteId = loteId;
        QuantidadeAmostragem = quantidadeAmostragem;
        UnidadeAmostragemId = unidadeAmostragemId;
        InformacoesComplementares = informacoesComplementares;
        Aprovado = aprovado;
    }

    public int SequenciaGroupTenant { get; set; }
    public DateOnly DataEmissao { get; set; }
    public Guid LoteId { get; set; }
    public decimal QuantidadeAmostragem { get; set; }
    public UnidadeMedidaAbreviacao UnidadeAmostragemId { get; set; }
    public string InformacoesComplementares { get; set; }
    public bool Aprovado { get; set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    #region Navigation Properties

    public virtual Lote Lote { get; set; }
    public virtual ICollection<CertificadoAnaliseEspecificacao> Especificacoes { get; set; } = new List<CertificadoAnaliseEspecificacao>();

    #endregion
}
