using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;
using Bootis.Catalogo.Domain.Enumerations;

using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Producao.Domain.Services.ReceitaManipulada;

public class ValidadorIncompatibilidadeService : IValidadorIncompatibilidadeService, IScopedService
{
    private readonly IProdutoIncompativelRepository _produtoIncompativelRepository;

    public ValidadorIncompatibilidadeService(IProdutoIncompativelRepository produtoIncompativelRepository)
    {
        _produtoIncompativelRepository = produtoIncompativelRepository;
    }

    public async Task ValidarIncompatibilidadesBloqueioAsync(IEnumerable<Produto> produtos)
    {
        var materiasPrimas = produtos.Where(p => p.ClasseProdutoId == TipoClasseProdutoAbreviacao.MateriaPrima).ToList();

        if (materiasPrimas.Count < 2)
            return;

        var produtoIds = materiasPrimas.Select(p => p.Id).ToList();

        var response = await ObterCenariosIncompatibilidadeAsync(produtoIds);

        if (response != null && response.Status == IncompatibilidadeStatus.Bloqueio)
        {
            var bloqueios = response.Incompatibilidades.Where(i => i.Nivel == NivelIncompatibilidade.Bloqueio).ToList();

            var mensagem = response.Tipo switch
            {
                IncompatibilidadeTipo.Unica => $"Incompatibilidade de Bloqueio detectada entre '{bloqueios.First().ProdutoDescricao}' e '{bloqueios.First().ProdutoIncompativelDescricao}'. Descrição: {bloqueios.First().Descricao}. A manipulação não pode ser realizada com estes componentes.",
                IncompatibilidadeTipo.Multipla => $"Múltiplas incompatibilidades de bloqueio detectadas ({bloqueios.Count} pares). A manipulação não pode ser realizada com estes componentes.",
                _ => "Incompatibilidades de bloqueio detectadas. A manipulação não pode ser realizada com estes componentes."
            };

            throw new ValidationException(mensagem);
        }
    }


}
