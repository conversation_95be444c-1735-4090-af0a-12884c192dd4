using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Producao.Domain.Services.ReceitaManipulada;

public class ValidadorIncompatibilidadeService : IValidadorIncompatibilidadeService, IScopedService
{
    private readonly IProdutoIncompativelRepository _produtoIncompativelRepository;

    public ValidadorIncompatibilidadeService(IProdutoIncompativelRepository produtoIncompativelRepository)
    {
        _produtoIncompativelRepository = produtoIncompativelRepository;
    }

    public async Task ValidarIncompatibilidadesBloqueioAsync(IEnumerable<Produto> produtos)
    {
        var materiasPrimas = produtos.Where(p => p.ClasseProdutoId == TipoClasseProdutoAbreviacao.MateriaPrima).ToList();

        if (materiasPrimas.Count < 2)
            return;

        var produtoIds = materiasPrimas.Select(p => p.Id).ToList();

        var response = await ObterCenariosIncompatibilidadeAsync(produtoIds);

        if (response != null)
        {
            var mensagem = response.Tipo switch
            {
                "Unica" => $"Incompatibilidade de Bloqueio detectada entre '{response.ParesIncompativeis.First().ProdutoDescricao}' e '{response.ParesIncompativeis.First().ProdutoIncompativelDescricao}'. Descrição: {response.ParesIncompativeis.First().Descricao}. A manipulação não pode ser realizada com estes componentes.",
                "Multipla" => $"Múltiplas incompatibilidades de bloqueio detectadas ({response.ParesIncompativeis.Count()} pares). A manipulação não pode ser realizada com estes componentes.",
                _ => "Incompatibilidades de bloqueio detectadas. A manipulação não pode ser realizada com estes componentes."
            };

            throw new ValidationException(mensagem);
        }
    }

    public async Task<IncompatibilidadeResponseDto?> ObterCenariosIncompatibilidadeAsync(IEnumerable<Guid> produtoIds)
    {
        var produtoIdsList = produtoIds.ToList();

        if (produtoIdsList.Count < 2)
            return new IncompatibilidadeResponseDto { Status = "OK" };
        
        var bloqueios = await _produtoIncompativelRepository
            .ObterIncompatibilidadesEntreAsync(produtoIdsList, NivelIncompatibilidade.Bloqueio);

        var bloqueiosList = DeduplicarPares(bloqueios.ToList());

        if (bloqueiosList.Count is not 0)
        {
            var paresIncompativeis = bloqueiosList.Select(i => new ParIncompativelDto
            {
                ProdutoId = i.ProdutoId,
                ProdutoDescricao = i.Produto?.Descricao ?? "Produto não encontrado",
                ProdutoIncompativelId = i.ProdutoIncompativelId,
                ProdutoIncompativelDescricao = i.ProdutoIncompatibilidade?.Descricao ?? "Produto não encontrado",
                Nivel = i.NivelIncompatibilidade.ToString(),
                Descricao = i.Descricao
            }).ToList();

            var tipo = bloqueiosList.Count == 1 ? "Unica" : "Multipla";
            
            MensagemUiDto mensagem;

            if (tipo == "Unica")
            {
                var primeiro = bloqueiosList.First();
                mensagem = new MensagemUiDto
                {
                    Titulo = "Incompatibilidade de Bloqueio",
                    Intro = "Foi detectada uma incompatibilidade de bloqueio:",
                    Itens =
                    [
                        $"{(primeiro.Produto?.Descricao ?? "Produto")} e {(primeiro.ProdutoIncompatibilidade?.Descricao ?? "Produto")} - {primeiro.Descricao}"
                    ],
                    Footer = "A manipulação não pode ser realizada com estes componentes."
                };
            }
            else
            {
                var itens = bloqueiosList
                    .Select(i => $"{(i.Produto?.Descricao ?? "Produto")} e {(i.ProdutoIncompatibilidade?.Descricao ?? "Produto")} - {i.Descricao}")
                    .OrderBy(x => x)
                    .ToList();

                mensagem = new MensagemUiDto
                {
                    Titulo = "Múltiplas Incompatibilidades de Bloqueio",
                    Intro = $"Foram detectadas {bloqueiosList.Count} incompatibilidades de bloqueio:",
                    Itens = itens,
                    Footer = "A manipulação não pode ser realizada com estes componentes."
                };
            }

            return new IncompatibilidadeResponseDto
            {
                Status = "Bloqueio",
                Tipo = tipo,
                TotalBloqueios = bloqueiosList.Count,
                Mensagem = mensagem,
                ParesIncompativeis = paresIncompativeis
            };
        }
        
        var avisos = await _produtoIncompativelRepository
            .ObterIncompatibilidadesEntreAsync(produtoIdsList, NivelIncompatibilidade.ApenasAviso);

        var avisosList = DeduplicarPares(avisos.ToList());

        if (avisosList.Count is not 0)
        {
            var tipo = avisosList.Count == 1 ? "Unica" : "Multipla";
            var itens = avisosList
                .Select(i => $"{(i.Produto?.Descricao ?? "Produto")} e {(i.ProdutoIncompatibilidade?.Descricao ?? "Produto")} - {i.Descricao}")
                .OrderBy(x => x)
                .ToList();

            MensagemUiDto mensagem;

            if (tipo == "Unica")
            {
                mensagem = new MensagemUiDto
                {
                    Titulo = "Incompatibilidade Detectada",
                    Intro = "Foram detectadas incompatibilidades entre os componentes selecionados:",
                    Itens = itens,
                    Footer = "Você pode prosseguir, mas deve estar ciente dessas incompatibilidades."
                };
            }
            else
            {
                mensagem = new MensagemUiDto
                {
                    Titulo = "Incompatibilidade Detectada",
                    Intro = "Foram identificadas incompatibilidades entre alguns componentes. Você pode prosseguir com o cadastro, mas será necessário considerar essa limitação.",
                    Itens = [],
                    Footer = ""
                };
            }

            return new IncompatibilidadeResponseDto
            {
                Status = "Aviso",
                Tipo = tipo,
                TotalBloqueios = 0,
                Mensagem = mensagem,
                ParesIncompativeis = new List<ParIncompativelDto>()
            };
        }

        return new IncompatibilidadeResponseDto { Status = "OK" };
    }

    private static List<ProdutoIncompativel> DeduplicarPares(List<ProdutoIncompativel> incompatibilidades)
    {
        var paresDeduplicated = new Dictionary<string, ProdutoIncompativel>();

        foreach (var incomp in incompatibilidades)
        {
            var menorId = incomp.ProdutoId < incomp.ProdutoIncompativelId ? incomp.ProdutoId : incomp.ProdutoIncompativelId;
            var maiorId = incomp.ProdutoId > incomp.ProdutoIncompativelId ? incomp.ProdutoId : incomp.ProdutoIncompativelId;
            var chave = $"{menorId}-{maiorId}";

            paresDeduplicated.TryAdd(chave, incomp);
        }

        return paresDeduplicated.Values.ToList();
    }
}
