using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;
using Bootis.Catalogo.Domain.Enumerations;

using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Producao.Domain.Services.ReceitaManipulada;

public class ValidadorIncompatibilidadeService : IValidadorIncompatibilidadeService, IScopedService
{
    private readonly IProdutoIncompativelRepository _produtoIncompativelRepository;

    public ValidadorIncompatibilidadeService(IProdutoIncompativelRepository produtoIncompativelRepository)
    {
        _produtoIncompativelRepository = produtoIncompativelRepository;
    }

    public async Task ValidarIncompatibilidadesBloqueioAsync(IEnumerable<Produto> produtos)
    {
        var materiasPrimas = produtos.Where(p => p.ClasseProdutoId == TipoClasseProdutoAbreviacao.MateriaPrima).ToList();

        if (materiasPrimas.Count < 2)
            return;

        var produtoIds = materiasPrimas.Select(p => p.Id).ToList();

        // Buscar incompatibilidades de bloqueio
        var incompatibilidades = await _produtoIncompativelRepository
            .ObterTodasIncompatibilidadesEntreAsync(produtoIds);

        var bloqueios = incompatibilidades
            .Where(i => i.NivelIncompatibilidade == NivelIncompatibilidade.Bloqueio)
            .ToList();

        if (bloqueios.Any())
        {
            var mensagem = bloqueios.Count == 1
                ? $"Incompatibilidade de Bloqueio detectada entre '{bloqueios.First().Produto?.Descricao}' e '{bloqueios.First().ProdutoIncompatibilidade?.Descricao}'. Descrição: {bloqueios.First().Descricao}. A manipulação não pode ser realizada com estes componentes."
                : $"Múltiplas incompatibilidades de bloqueio detectadas ({bloqueios.Count} pares). A manipulação não pode ser realizada com estes componentes.";

            throw new ValidationException(mensagem);
        }
    }


}
