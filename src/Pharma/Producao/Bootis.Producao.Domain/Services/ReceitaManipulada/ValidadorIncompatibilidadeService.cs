using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Validar;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Producao.Domain.Services.ReceitaManipulada;

public class ValidadorIncompatibilidadeService : IValidadorIncompatibilidadeService, IScopedService
{
    private readonly IProdutoIncompativelRepository _produtoIncompativelRepository;

    public ValidadorIncompatibilidadeService(IProdutoIncompativelRepository produtoIncompativelRepository)
    {
        _produtoIncompativelRepository = produtoIncompativelRepository;
    }

    public async Task ValidarIncompatibilidadesBloqueioAsync(IEnumerable<Produto> produtos)
    {
        var materiasPrimas = produtos.Where(p => p.ClasseProdutoId == TipoClasseProdutoAbreviacao.MateriaPrima).ToList();

        if (materiasPrimas.Count < 2)
            return;

        var produtoIds = materiasPrimas.Select(p => p.Id).ToList();

        var response = await ObterCenariosIncompatibilidadeAsync(produtoIds);

        if (response != null && response.Status == IncompatibilidadeStatus.Bloqueio)
        {
            var bloqueios = response.Incompatibilidades.Where(i => i.Nivel == NivelIncompatibilidade.Bloqueio).ToList();

            var mensagem = response.Tipo switch
            {
                IncompatibilidadeTipo.Unica => $"Incompatibilidade de Bloqueio detectada entre '{bloqueios.First().ProdutoDescricao}' e '{bloqueios.First().ProdutoIncompativelDescricao}'. Descrição: {bloqueios.First().Descricao}. A manipulação não pode ser realizada com estes componentes.",
                IncompatibilidadeTipo.Multipla => $"Múltiplas incompatibilidades de bloqueio detectadas ({bloqueios.Count} pares). A manipulação não pode ser realizada com estes componentes.",
                _ => "Incompatibilidades de bloqueio detectadas. A manipulação não pode ser realizada com estes componentes."
            };

            throw new ValidationException(mensagem);
        }
    }

    public async Task<IncompatibilidadeResponseDto?> ObterCenariosIncompatibilidadeAsync(IEnumerable<Guid> produtoIds)
    {
        var produtoIdsList = produtoIds.ToList();

        if (produtoIdsList.Count < 2)
            return new IncompatibilidadeResponseDto { Status = IncompatibilidadeStatus.Ok };

        // Uma única consulta ao banco para buscar todas as incompatibilidades
        var todasIncompatibilidades = await _produtoIncompativelRepository
            .ObterTodasIncompatibilidadesEntreAsync(produtoIdsList);

        var incompatibilidadesList = DeduplicarPares(todasIncompatibilidades.ToList());

        if (incompatibilidadesList.Count == 0)
            return new IncompatibilidadeResponseDto { Status = IncompatibilidadeStatus.Ok };

        // Separar por nível de incompatibilidade
        var bloqueios = incompatibilidadesList.Where(i => i.NivelIncompatibilidade == NivelIncompatibilidade.Bloqueio).ToList();
        var avisos = incompatibilidadesList.Where(i => i.NivelIncompatibilidade == NivelIncompatibilidade.ApenasAviso).ToList();

        // Converter para DTOs
        var incompatibilidadesDto = incompatibilidadesList.Select(i => new ParIncompativelDto
        {
            ProdutoId = i.ProdutoId,
            ProdutoDescricao = i.Produto?.Descricao ?? "Produto não encontrado",
            ProdutoIncompativelId = i.ProdutoIncompativelId,
            ProdutoIncompativelDescricao = i.ProdutoIncompatibilidade?.Descricao ?? "Produto não encontrado",
            Nivel = i.NivelIncompatibilidade,
            Descricao = i.Descricao
        }).ToList();

        // Determinar status e tipo baseado na presença de bloqueios
        var status = bloqueios.Count > 0 ? IncompatibilidadeStatus.Bloqueio : IncompatibilidadeStatus.Aviso;

        // O tipo é determinado pela quantidade de incompatibilidades do nível que define o status
        var incompatibilidadesQueDefinem = status == IncompatibilidadeStatus.Bloqueio ? bloqueios : avisos;
        var tipo = incompatibilidadesQueDefinem.Count == 1 ? IncompatibilidadeTipo.Unica : IncompatibilidadeTipo.Multipla;

        return new IncompatibilidadeResponseDto
        {
            Status = status,
            Tipo = tipo,
            TotalBloqueios = bloqueios.Count,
            TotalAvisos = avisos.Count,
            Incompatibilidades = incompatibilidadesDto
        };
    }

    private static List<ProdutoIncompativel> DeduplicarPares(List<ProdutoIncompativel> incompatibilidades)
    {
        var paresDeduplicated = new Dictionary<string, ProdutoIncompativel>();

        foreach (var incomp in incompatibilidades)
        {
            var menorId = incomp.ProdutoId < incomp.ProdutoIncompativelId ? incomp.ProdutoId : incomp.ProdutoIncompativelId;
            var maiorId = incomp.ProdutoId > incomp.ProdutoIncompativelId ? incomp.ProdutoId : incomp.ProdutoIncompativelId;
            var chave = $"{menorId}-{maiorId}";

            paresDeduplicated.TryAdd(chave, incomp);
        }

        return paresDeduplicated.Values.ToList();
    }
}
