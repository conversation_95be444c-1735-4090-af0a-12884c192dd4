using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAssociadoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoExcipienteAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Estoque.Domain.Dtos.Lote;
using Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Producao.Domain.Services.ReceitaManipulada;

public class ReceitaManipuladaDomainService(
    IMediator mediator,
    IReceitaManipuladaRepository receitaManipuladaRepositoryRepository)
    : IReceitaManipuladaDomainService
{
    public async Task<ProdutoTipoCapsula> ObterProdutoTipoCapsulaPorIdAsync(Guid produtoCapsulaId)
    {
        var produtoCapsula =
            await receitaManipuladaRepositoryRepository.ObterProdutoTipoCapsulaPorProdutoIdAsync(produtoCapsulaId);

        if (produtoCapsula == null)
            throw new DomainException(Localizer.Instance.GetMessage_Capsula_NaoDisponivel());

        return produtoCapsula;
    }


    public async Task<FormulaPadrao> ObterFormulaPadraoAsync(Guid produtoId)
    {
        var formulaPadrao = await receitaManipuladaRepositoryRepository.ObterFormulaPadraoPorProdutoIdAsync(produtoId);

        return formulaPadrao ?? null;
    }

    public async Task<IEnumerable<ProdutoAssociado>> ObterProdutosAssociadosAsync(Guid produtoId,
        Guid formaFarmaceuticaId)
    {
        var produto =
            await receitaManipuladaRepositoryRepository.ObterProdutoIncludeAssociadoPorIdAsync(produtoId,
                formaFarmaceuticaId);

        var produtosAssociados = produto.ProdutoAssociado?.ToList();

        return produtosAssociados ?? [];
    }

    public async Task<ProdutoExcipiente> ObterExcipienteAsync(TipoClasseBiofarmaceutica classeBiofarmaceutica)
    {
        return await receitaManipuladaRepositoryRepository.ObterPorClasseBiofarmaceuticaAsync(classeBiofarmaceutica);
    }

    public async Task<decimal> ObterDescontoPrescritorAsync(Guid? prescritorId)
    {
        if (prescritorId.HasValue) return await mediator.Send(new ObterDescontoPrescritorDto(prescritorId.Value));

        return 0;
    }

    public async Task<(DateOnly dataValidade, RegraDataValidade regra)> RegraDataValidadeItemAsync(Produto produto,
        FormaFarmaceutica formaFarmaceutica)
    {
        var dataAtual = DateOnly.FromDateTime(DateTime.UtcNow.Date);

        var validadeLote = await mediator.Send(new ObterDataValidadeDto(produto.Id));

        DateOnly? validadeMateriaPrima = produto.ProdutoMateriaPrima?.DiasValidade is not null
            ? dataAtual.AddDays((int)produto.ProdutoMateriaPrima.DiasValidade)
            : null;

        DateOnly validadePrincipioAtivo;

        if (validadeLote is null && validadeMateriaPrima is null)
            validadePrincipioAtivo = DateOnly.MaxValue;
        else if (validadeLote is null)
            validadePrincipioAtivo = validadeMateriaPrima!.Value;
        else if (validadeMateriaPrima is null)
            validadePrincipioAtivo = validadeLote.Value;
        else
            validadePrincipioAtivo = validadeLote < validadeMateriaPrima
                ? validadeLote.Value
                : validadeMateriaPrima.Value;

        var validadeFormaFarmaceutica = dataAtual.AddDays(formaFarmaceutica.ValidadeDias);

        var dataValidadeItem = validadeFormaFarmaceutica > validadePrincipioAtivo
            ? validadePrincipioAtivo
            : validadeFormaFarmaceutica;

        var regraDataValidade = validadeFormaFarmaceutica > validadePrincipioAtivo
            ? RegraDataValidade.PrincipioAtivo
            : RegraDataValidade.FormaFarmaceutica;

        if (dataValidadeItem < dataAtual)
            throw new DomainException(
                Localizer.Instance.GetMessage_ReceitaManipuladaItem_DataValidadeNaoEncontrada(produto.Descricao));

        return (dataValidadeItem, regraDataValidade);
    }

    public async Task<ProdutoEmbalagem> EscolherEmbalagemAsync(decimal quantidadeCapsulas, Guid capsulaTamanhoId)
    {
        var embalagemId =
            await mediator.Send(new ObterEmbalagemCapsulaTamanhoAssociacoesDto(quantidadeCapsulas, capsulaTamanhoId));

        if (embalagemId is not null)
        {
            return await receitaManipuladaRepositoryRepository.ObterProdutoEmbalagemPorIdAsync(embalagemId.Value);
        }
        
        var embalagensMultiplas = await EscolherEmbalagensAsync(quantidadeCapsulas, capsulaTamanhoId);
        
        if (embalagensMultiplas.Count is 0)
            throw new DomainException(Localizer.Instance.GetMessage_Embalagem_NaoDisponivel());
            
        return embalagensMultiplas.First().embalagem;
    }

    private async Task<List<(ProdutoEmbalagem embalagem, int quantidade)>> EscolherEmbalagensAsync(decimal quantidadeCapsulas, Guid capsulaTamanhoId)
    {
        var todasEmbalagens = await mediator.Send(new ObterTodasEmbalagensCapsulaDto(capsulaTamanhoId));

        if (todasEmbalagens.Count is 0)
            throw new DomainException(Localizer.Instance.GetMessage_Embalagem_NaoDisponivel());

        var resultado = new List<(ProdutoEmbalagem embalagem, int quantidade)>();
        var quantidadeRestante = quantidadeCapsulas;
        
        foreach (var embalagemInfo in todasEmbalagens)
        {
            if (quantidadeRestante <= 0) break;

            var quantidadeDestaEmbalagem = (int)Math.Floor(quantidadeRestante / embalagemInfo.QuantidadeCapsula);
            
            if (quantidadeDestaEmbalagem > 0)
            {
                var produtoEmbalagem = await receitaManipuladaRepositoryRepository.ObterProdutoEmbalagemPorIdAsync(embalagemInfo.ProdutoEmbalagemId);
                resultado.Add((produtoEmbalagem, quantidadeDestaEmbalagem));
                quantidadeRestante -= quantidadeDestaEmbalagem * embalagemInfo.QuantidadeCapsula;
            }
        }
        
        switch (quantidadeRestante)
        {
            case > 0 when todasEmbalagens.Count is not 0:
            {
                var menorEmbalagem = todasEmbalagens.Last();
                var produtoEmbalagem = await receitaManipuladaRepositoryRepository.ObterProdutoEmbalagemPorIdAsync(menorEmbalagem.ProdutoEmbalagemId);
            
                var embalagemExistente = resultado.FirstOrDefault(r => r.embalagem.ProdutoId == produtoEmbalagem.ProdutoId);
                if (embalagemExistente != default)
                {
                    var index = resultado.IndexOf(embalagemExistente);
                    resultado[index] = (embalagemExistente.embalagem, embalagemExistente.quantidade + 1);
                }
                else
                {
                    resultado.Add((produtoEmbalagem, 1));
                }

                break;
            }
        }

        return resultado;
    }

    public async Task<CalculoReceitaManipulada.ResultadoCapsula> EscolherCapsulaAsync(decimal volumeMinimoRecipiente)
    {
        var produtosCapsulaDto = (await mediator.Send(new ObterProdutosCapsulaRequestDto()))
            .Where(p => p.VolumeMl > 0)
            .ToList();

        if (!produtosCapsulaDto.Any())
            throw new DomainException(Localizer.Instance.GetMessage_Capsula_NaoDisponivel());

        var produtoEscolhido = EscolherCapsulaComVolumeSuficiente(produtosCapsulaDto, volumeMinimoRecipiente);

        if (produtoEscolhido != null)
        {
            var produtoCapsulaDireto = await ObterProdutoTipoCapsulaPorProdutoIdAsync(produtoEscolhido.ProdutoId);
            var volumeCapsula = produtoEscolhido.VolumeMl;

            if (volumeCapsula < volumeMinimoRecipiente)
                throw new DomainException("Volume da cápsula é insuficiente para acomodar a dose.");

            var volumeExcipienteDireto = volumeCapsula - volumeMinimoRecipiente;

            return new CalculoReceitaManipulada.ResultadoCapsula(
                produtoCapsulaDireto,
                volumeExcipienteDireto,
                1
            );
        }

        produtoEscolhido = EscolherMelhorDivisao(produtosCapsulaDto, volumeMinimoRecipiente,
            out var volumeExcipiente, out var divisaoDose);

        if (produtoEscolhido == null)
            throw new DomainException(Localizer.Instance.GetMessage_Capsula_NaoDisponivel());

        var produtoCapsula = await ObterProdutoTipoCapsulaPorProdutoIdAsync(produtoEscolhido.ProdutoId);

        return new CalculoReceitaManipulada.ResultadoCapsula(
            produtoCapsula, volumeExcipiente, divisaoDose
        );
    }

    public async Task<ReceitaManipuladaRastreioCalculo.FatoresTotais> ObterFatoresTotaisAsync(
        Produto produtoUsado,
        Produto produtoOrigem,
        ReceitaManipuladaItem itemOrigem,
        LoteIdComSaldoDto lote = null)
    {
        var isSinonimo = produtoOrigem != null && produtoUsado.Id != produtoOrigem.Id;

        var fatorEquivalencia = produtoUsado.ProdutoMateriaPrima?.FatorEquivalencia ?? 1;
        var fatorFornecedor = produtoUsado.ProdutoMateriaPrima?.FatorDiluicaoFornecedor ?? 1;

        decimal? fatorEquivalenciaSinonimo = null;
        decimal? fatorCorrecaoSinonimo = null;

        if (isSinonimo)
        {
            var sinonimo =
                await receitaManipuladaRepositoryRepository.ObterFatoresSinonimoAsync(produtoOrigem.Id,
                    produtoUsado.Id);
            if (sinonimo is not null)
            {
                fatorEquivalenciaSinonimo = sinonimo.FatorEquivalencia;
                fatorCorrecaoSinonimo = sinonimo.FatorCorrecao;

                fatorEquivalencia = fatorEquivalenciaSinonimo ?? fatorEquivalencia;
                fatorFornecedor = fatorCorrecaoSinonimo ?? fatorFornecedor;
            }
        }
        else if (itemOrigem is ReceitaManipuladaItemSinonimo s)
        {
            fatorEquivalenciaSinonimo = s.FatorEquivalencia;
            fatorCorrecaoSinonimo = s.FatorCorrecao;

            fatorEquivalencia = fatorEquivalenciaSinonimo ?? fatorEquivalencia;
            fatorFornecedor = fatorCorrecaoSinonimo ?? fatorFornecedor;
        }

        var fatorDiluicaoInterna =
            lote?.FatorDiluicaoInterna ?? produtoUsado.ProdutoMateriaPrima?.FatorDiluicaoInterna ?? 1;
        var fatorConcentracaoAgua =
            lote?.FatorConcentracaoAgua ?? produtoUsado.ProdutoMateriaPrima?.FatorConcentracaoAgua ?? 1;
        var densidade = lote?.Densidade ?? produtoUsado.ProdutoMateriaPrima?.Densidade ?? 1;

        var valencia = (produtoUsado.ProdutoMateriaPrima?.Valencia ?? 0) > 0
            ? produtoUsado.ProdutoMateriaPrima.Valencia.Value
            : 1;

        var fatorTotal = fatorEquivalencia * fatorDiluicaoInterna * fatorConcentracaoAgua * valencia * fatorFornecedor;

        return new ReceitaManipuladaRastreioCalculo.FatoresTotais
        {
            FatorEquivalencia = fatorEquivalencia,
            FatorFornecedor = fatorFornecedor,
            FatorDiluicaoInterna = fatorDiluicaoInterna,
            FatorConcentracaoAgua = fatorConcentracaoAgua,
            Densidade = densidade,
            FatorTotal = fatorTotal,
            FatorSinonimo = fatorEquivalenciaSinonimo,
            FatorCorrecao = fatorCorrecaoSinonimo
        };
    }

    public async Task<List<CalculoReceitaManipulada.EmbalagemQspFracionada>> EscolherEmbalagensQspFracionadoAsync(
        decimal volumeTotalMl)
    {
        var todasEmbalagens = await receitaManipuladaRepositoryRepository
            .ObterTodasEmbalagensDisponiveisAsync();

        var embalagensValidas = todasEmbalagens
            .Where(e => e.Produto.ProdutoEmbalagem?.Volume is > 0)
            .OrderByDescending(e => e.Produto.ProdutoEmbalagem.Volume.Value)
            .ToList();

        if (embalagensValidas.Count is 0)
            throw new DomainException("Nenhuma embalagem disponível com volume configurado.");

        var resultado = new List<CalculoReceitaManipulada.EmbalagemQspFracionada>();
        var volumeRestante = volumeTotalMl;

        foreach (var embalagem in embalagensValidas)
        {
            var volumeEmbalagem = embalagem.Produto.ProdutoEmbalagem.Volume!.Value;
            if (volumeRestante <= 0)
                break;

            var quantidade = (int)Math.Floor(volumeRestante / volumeEmbalagem);

            if (quantidade == 0)
            {
                resultado.Add(new CalculoReceitaManipulada.EmbalagemQspFracionada(embalagem, 1));
                break;
            }

            resultado.Add(new CalculoReceitaManipulada.EmbalagemQspFracionada(embalagem, quantidade));
            volumeRestante -= quantidade * volumeEmbalagem;
        }

        return resultado;
    }

    private static ObterProdutosCapsulaResponseDto EscolherCapsulaComVolumeSuficiente(
        IEnumerable<ObterProdutosCapsulaResponseDto> produtosCapsulaDto, decimal volumeMinimoRecipiente)
    {
        return produtosCapsulaDto
            .FirstOrDefault(produto => volumeMinimoRecipiente <= produto.VolumeMl);
    }

    private static ObterProdutosCapsulaResponseDto EscolherMelhorDivisao(
        IEnumerable<ObterProdutosCapsulaResponseDto> produtosCapsulaDto, decimal volumeMinimoRecipiente,
        out decimal quantidadeQsp, out decimal melhorDivisaoDose)
    {
        ObterProdutosCapsulaResponseDto produtoEscolhido = null;
        quantidadeQsp = decimal.MaxValue;
        melhorDivisaoDose = decimal.MaxValue;

        foreach (var produto in produtosCapsulaDto)
        {
            var volume = produto.VolumeMl;

            if (volume <= 0) continue;

            var divisaoDose = Math.Ceiling(volumeMinimoRecipiente / volume);
            var volumeNecessarioPorDose = volumeMinimoRecipiente / divisaoDose;

            if (volumeNecessarioPorDose <= volume)
            {
                var qspAtual = volume - volumeNecessarioPorDose;

                if (divisaoDose < melhorDivisaoDose ||
                    (divisaoDose == melhorDivisaoDose && qspAtual < quantidadeQsp))
                {
                    produtoEscolhido = produto;
                    quantidadeQsp = qspAtual;
                    melhorDivisaoDose = divisaoDose;
                }
            }
        }

        return produtoEscolhido;
    }

    private async Task<ProdutoTipoCapsula> ObterProdutoTipoCapsulaPorProdutoIdAsync(Guid produtoId)
    {
        var produtoCapsula =
            await receitaManipuladaRepositoryRepository.ObterProdutoTipoCapsulaPorProdutoIdAsync(produtoId);
        if (produtoCapsula == null) throw new DomainException(Localizer.Instance.GetMessage_Capsula_NaoDisponivel());

        return produtoCapsula;
    }
}