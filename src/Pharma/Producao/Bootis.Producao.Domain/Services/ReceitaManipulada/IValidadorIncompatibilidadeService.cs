using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;

namespace Bootis.Producao.Domain.Services.ReceitaManipulada;

public interface IValidadorIncompatibilidadeService
{
    Task ValidarIncompatibilidadesBloqueioAsync(IEnumerable<Produto> produtos);
    Task<IncompatibilidadeResponse?> ObterCenariosIncompatibilidadeAsync(IEnumerable<Guid> produtoIds);
}