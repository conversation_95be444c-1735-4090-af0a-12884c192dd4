using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;

namespace Bootis.Producao.Domain.Services.ReceitaManipulada;

public interface IValidadorIncompatibilidadeService
{
    Task ValidarIncompatibilidadesBloqueioAsync(IEnumerable<Produto> produtos);
    Task<IncompatibilidadeResponseDto?> ObterCenariosIncompatibilidadeAsync(IEnumerable<Guid> produtoIds);
}