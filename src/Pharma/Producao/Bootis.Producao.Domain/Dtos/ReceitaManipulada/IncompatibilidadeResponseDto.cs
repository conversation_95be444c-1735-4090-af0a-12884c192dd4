namespace Bootis.Producao.Domain.Dtos.ReceitaManipulada;

public class IncompatibilidadeResponseDto
{
    public string Status { get; set; } = "OK";
    public string Tipo { get; set; } = string.Empty;
    public MensagemUiDto? Mensagem { get; set; }
    public int TotalBloqueios { get; set; } = 0;
    public IEnumerable<ParIncompativelDto> ParesIncompativeis { get; set; } = new List<ParIncompativelDto>();
}

public class ParIncompativelDto
{
    public Guid ProdutoId { get; set; }
    public string ProdutoDescricao { get; set; } = string.Empty;
    public Guid ProdutoIncompativelId { get; set; }
    public string ProdutoIncompativelDescricao { get; set; } = string.Empty;
    public string Nivel { get; set; } = string.Empty;
    public string Descricao { get; set; } = string.Empty;
}
