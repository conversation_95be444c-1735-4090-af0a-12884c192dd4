using Bootis.Catalogo.Domain.Enumerations;

namespace Bootis.Producao.Domain.Dtos.ReceitaManipulada;

public class IncompatibilidadeResponseDto
{
    public IncompatibilidadeStatus Status { get; set; } = IncompatibilidadeStatus.Ok;
    public IncompatibilidadeTipo Tipo { get; set; } = IncompatibilidadeTipo.Nenhuma;
    public int TotalBloqueios { get; set; } = 0;
    public int TotalAvisos { get; set; } = 0;
    public IEnumerable<ParIncompativelDto> Incompatibilidades { get; set; } = [];
}

public class ParIncompativelDto
{
    public Guid ProdutoId { get; set; }
    public string ProdutoDescricao { get; set; } = string.Empty;
    public Guid ProdutoIncompativelId { get; set; }
    public string ProdutoIncompativelDescricao { get; set; } = string.Empty;
    public NivelIncompatibilidade Nivel { get; set; }
    public string Descricao { get; set; } = string.Empty;
}

public enum IncompatibilidadeStatus
{
    Ok = 0,
    Aviso = 1,
    Bloqueio = 2
}

public enum IncompatibilidadeTipo
{
    Nenhuma = 0,
    Unica = 1,
    Multipla = 2
}
