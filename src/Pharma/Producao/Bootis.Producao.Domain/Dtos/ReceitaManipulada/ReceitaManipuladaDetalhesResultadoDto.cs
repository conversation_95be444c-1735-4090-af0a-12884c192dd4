using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Domain.Enumerations;

namespace Bootis.Producao.Domain.Dtos.ReceitaManipulada;

public class ReceitaManipuladaDetalhesResultadoDto
{
    public DetalhesReceitaDto DetalhesReceita { get; set; }
    public DetalhesCalculoDto DetalhesCalculo { get; set; }

    public class DetalhesReceitaDto
    {
        public Guid Id { get; set; }

        public string PacienteDescricao { get; set; }
        public Guid PacienteId { get; set; }

        public string PrescritorDescricao { get; set; }
        public Guid? PrescritorId { get; set; }

        public string FormaFarmaceuticaDescricao { get; set; }
        public string FormaFarmaceuticaApresentacao { get; set; }
        public Guid FormaFarmaceuticaId { get; set; }

        public string CapsulaDescricao { get; set; }
        public Guid? CapsulaId { get; set; }

        public string EmbalagemDescricao { get; set; }
        public Guid? EmbalagemId { get; set; }

        public string PosologiaDescricao { get; set; }
        public Guid? PosologiaId { get; set; }

        public decimal QuantidadeReceita { get; set; }
        public int QuantidadeRepetir { get; set; }
        public decimal? QuantidadeDose { get; set; }
        
        public bool UsoContinuo { get; set; }
        public TipoCalculoUsoContinuo? TipoUsoContinuo { get; set; }
        public decimal? QuantidadeReSell { get; set; }
        public PeriodosPosologia? PeriodicidadeReSell { get; set; }
        public int? DuracaoPrevistaReSellDias { get; set; }
        public DateTime? PrevisaoTerminoReSell { get; set; }

        public string Observacao { get; set; }
        public DateTime PrevisaoEntrega { get; set; }

        public List<ReceitaManipuladaItemPrescritoDto> Componentes { get; set; } = new();
        public ReceitaManipuladaInformacoesAdicionaisDto InformacoesAdicionais { get; set; }
    }

    public class DetalhesCalculoDto
    {
        public string FormaFarmaceuticaDescricao { get; set; }
        public string FormaFarmaceuticaApresentacao { get; set; }
        public Guid FormaFarmaceuticaId { get; set; }

        public string CapsulaDescricao { get; set; }
        public decimal QuantidadeCapsula { get; set; }

        public string EmbalagemDescricao { get; set; }
        public decimal QuantidadeEmbalagem { get; set; }

        public decimal VolumeCalculado { get; set; }
        public decimal? QuantidadeDose { get; set; }

        public List<ReceitaManipuladaItemCalculadoDto> ComponentesCalculados { get; set; } = new();

        public DetalhesValoresDto DetalhesValores { get; set; }
        public DetalhesVolumeDto DetalhesVolume { get; set; }
    }

    public class ReceitaManipuladaItemPrescritoDto
    {
        public string DescricaoProduto { get; set; }
        public Guid ProdutoId { get; set; }
        public decimal Quantidade { get; set; }
        public int UnidadeMedidaId { get; set; }
        public string UnidadeMedidaAbreviacao { get; set; }
        public TipoComponente TipoQuantificacao { get; set; }
    }

    public class ReceitaManipuladaItemCalculadoDto
    {
        public string DescricaoProduto { get; set; }
        public decimal QuantidadePrescrita { get; set; }
        public int UnidadeMedidaPrescritaId { get; set; }
        public string UnidadeMedidaPrescrita { get; set; }
        public decimal QuantidadeCalculada { get; set; }
        public int UnidadeMedidaCalculadaId { get; set; }
        public string UnidadeMedidaCalculada { get; set; }
        public decimal? Volume { get; set; }
        public decimal? Fator { get; set; }
        public decimal? Densidade { get; set; }

        public bool? IsExcipiente { get; set; }
        public bool? IsQsp { get; set; }
        public bool? IsAssociado { get; set; }
        public bool? IsEmbalagem { get; set; }
        public bool? IsCapsula { get; set; }

        public string TipoVisualizacao =>
            IsEmbalagem == true ? "Embalagem"
            : IsCapsula == true ? "Capsula"
            : IsAssociado == true ? "Associado"
            : ComponentesFormula?.Any() == true ? "Formula"
            : ProdutoOrigemId != null ? "Componente Formula"
            : "Componente";

        public bool EhSinonimo { get; set; }
        public bool EhEditadoNoRotulo { get; set; }
        public TipoComponente? TipoQuantificacao { get; set; }
        public TipoComponenteReceita? TipoComponenteReceita { get; set; }

        public bool DesmembraFormula { get; set; }
        public bool? AssociadoAcumula { get; set; }
        public decimal? AssociadoDosagemMaxima { get; set; }
        public decimal? AssociadoDosagemMinima { get; set; }
        public int? AssociadoUnidadeMedidaDosagemId { get; set; }
        public TipoOrigem? TipoOrigem { get; set; }
        public FormulaPadraoDesmembramento? TipoDesmembramento { get; set; }

        public string ProdutoSinonimoDescricao { get; set; }
        public string ProdutoDescricaoOriginal { get; set; }
        public string ProdutoDescricaoEditada { get; set; }

        public Guid? ProdutoOrigemId { get; set; }
        public Guid? ProdutoSinonimoId { get; set; }
        public Guid? ProdutoId { get; set; }

        public decimal? VolumeMaximoCapsula { get; set; }
        public Guid? TamanhoCapsulaId { get; set; }
        public decimal? CapacidadeMaximaEmbalagem { get; set; }

        public decimal PrecoCusto { get; set; }
        public decimal PercentualLucro { get; set; }
        public decimal PrecoVenda { get; set; }

        public List<ReceitaManipuladaItemCalculadoDto> ComponentesFormula { get; set; } = new();
        public FatoresDto Fatores { get; set; }
    }

    public class FatoresDto
    {
        public decimal? Equivalencia { get; set; }
        public decimal? DiluicaoFornecedor { get; set; }
        public decimal? ConcentracaoAgua { get; set; }
        public decimal? DiluicaoInterna { get; set; }
        public decimal? Sinonimo { get; set; }
        public decimal? FatorCorrecao { get; set; }
        public decimal? FatorTotal { get; set; }
    }

    public class ReceitaManipuladaInformacoesAdicionaisDto
    {
        public DateTime? DataEmissao { get; set; }
        public DateOnly? DataPrescricao { get; set; }
        public DateTime? DataValidadePrescricao { get; set; }
        public DateOnly? DataValidadeReceita { get; set; }
    }

    public class DetalhesValoresDto
    {
        public decimal PrecoBruto { get; set; }
        public decimal Desconto { get; set; }
        public decimal PrecoReceita { get; set; }
        public decimal CustoOperacional { get; set; }

        public decimal PrecoCustoTotal { get; set; }
        public decimal PrecoVendaTotal { get; set; }

        public decimal Lucro { get; set; }
        public decimal PercentualLucroTotal { get; set; }
    }

    public class DetalhesVolumeDto
    {
        public TipoCalculo? TipoCalculoReceita { get; set; }
        public decimal VolumeMateriasPrimas { get; set; }
        public decimal VolumeExcipiente { get; set; }
        public decimal? PercentualMinimoExcipiente { get; set; }
        public decimal VolumeTotalMinimo { get; set; }
        public decimal? DivisaoDose { get; set; }

        public string DescricaoCapsula { get; set; }
        public Guid? NumeroCapsulaId { get; set; }
        public decimal? VolumeCapsula { get; set; }

        public string DescricaoEmbalagem { get; set; }
        public decimal? VolumeEmbalagem { get; set; }
    }
}