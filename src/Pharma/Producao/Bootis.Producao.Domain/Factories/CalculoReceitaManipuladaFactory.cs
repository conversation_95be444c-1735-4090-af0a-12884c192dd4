using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Estoque.Domain.Services;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Domain.Services.ReceitaManipulada;
using Bootis.Shared.Common.Interfaces;

namespace Bootis.Producao.Domain.Factories;

public class CalculoReceitaManipuladaFactory(
    ILoteSeletorService loteSeletorService,
    IReceitaManipuladaDomainService domainService)
    : ICalculoReceitaManipuladaFactory, IScopedService
{
    public CalculoReceitaManipulada Criar(
        ICollection<ReceitaManipuladaItem> itensEntrada,
        decimal quantidadeAlvoCalculo,
        FormaFarmaceutica formaFarmaceuticaEntrada,
        Cliente pacienteEntrada,
        Guid? prescritorIdEntrada,
        TipoDesconto? tipoDescontoManualEntrada,
        decimal? descontoManualEntrada,
        decimal? percentualDescontoManualEntrada)
    {
        return new CalculoReceitaManipulada(
            itensEntrada,
            quantidadeAlvoCalculo,
            formaFarmaceuticaEntrada,
            pacienteEntrada,
            prescritorIdEntrada,
            tipoDescontoManualEntrada,
            descontoManualEntrada,
            percentualDescontoManualEntrada,
            domainService,
            loteSeletorService);
    }
}