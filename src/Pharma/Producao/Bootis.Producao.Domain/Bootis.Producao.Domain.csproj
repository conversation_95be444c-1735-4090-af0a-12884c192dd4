<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{3dea5d5f-4549-4f4a-b05c-638c61404560}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Estoque\Bootis.Estoque.Domain\Bootis.Estoque.Domain.csproj"/>
        <ProjectReference Include="..\Bootis.Producao.Resources\Bootis.Producao.Resources.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="MediatR" Version="13.0.0"/>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>

