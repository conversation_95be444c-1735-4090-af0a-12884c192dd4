using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Pessoa.Domain.Enumerations;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Producao.Application.Extensions;

public static class ReceitaManipuladaMappingExtensions
{
    public static ReceitaManipuladaResultadoDto MapearParaResultadoDto(
        this ReceitaManipulada receita,
        CalculoReceitaManipulada calculo)
    {
        return new ReceitaManipuladaResultadoDto
        {
            QuantidadeReceita = receita.QuantidadeReceita,
            QuantidadeRepetir = receita.QuantidadeRepetir,
            QuantidadeDose = receita.QuantidadeDose,
            
            UsoContinuo = receita.UsoContinuo,
            TipoUsoContinuo = receita.TipoUsoContinuo,
            QuantidadeReSell = receita.QuantidadeReSell,
            PeriodicidadeReSell = receita.PeriodicidadeReSell,
            DuracaoPrevistaReSellDias = receita.DuracaoPrevistaReSellDias,
            PrevisaoTerminoReSell = receita.PrevisaoTerminoReSell,
            
            FormaFarmaceuticaApresentacao = receita.FormaFarmaceutica?.Apresentacao,
            FormaFarmaceuticaId = receita.FormaFarmaceutica?.Id ?? Guid.Empty,
            
            PacienteDescricao = receita.Paciente?.Nome,
            PacienteId = receita.Paciente?.Id ?? Guid.Empty,
            PacienteTipoPessoa = (TipoPessoa)receita.Paciente?.Pessoa!,
            PacienteCnpj = receita.Paciente?.Cnpj,
            PacienteCpf = receita.Paciente?.Cpf,
            
            PrescritorDescricao = receita.Prescritor?.NomeCompleto,
            PrescritorId = receita.Prescritor?.Id,
            PrescritorSiglaRegistro = receita.Prescritor?.TipoRegistro?.Sigla,
            PrescritorCodigoRegistro = receita.Prescritor?.CodigoRegistro,
            PrescritorUfRegistro = receita.Prescritor?.UfRegistro?.Abreviacao,
            
            Componentes = receita.Itens
                .OrderBy(i => i.Ordem)
                .Select(item =>
                {
                    var descricaoFinal =
                        !string.IsNullOrWhiteSpace(item.DescricaoProduto) &&
                        item.DescricaoProduto != item.Produto?.Descricao &&
                        item.DescricaoProduto != (item as ReceitaManipuladaItemSinonimo)?.ProdutoSinonimo?.Sinonimo
                            ? item.DescricaoProduto
                            : (item as ReceitaManipuladaItemSinonimo)?.ProdutoSinonimo?.Sinonimo
                              ?? item.Produto?.Descricao;

                    return new ReceitaManipuladaItemResultadoDto
                    {
                        DescricaoProduto = descricaoFinal?.Trim(),
                        ProdutoId = item.Produto.Id,
                        Quantidade = item.Quantidade,
                        UnidadeMedidaId = item.UnidadeMedidaId,
                        UnidadeMedidaAbreviacao = ((UnidadeMedidaAbreviacao)item.UnidadeMedidaId).ToString(),
                        TipoQuantificacao = item.Tipo,
                        Ordem = item.Ordem,
                        FormulaPadraoId =
                            calculo.FormulasPadraoPorProdutoId.TryGetValue(item.Produto.Id, out var formulaId)
                                ? formulaId
                                : null
                    };
                }).ToList()
        };
    }

    public static ReceitaManipuladaDetalhesResultadoDto MapearParaResultadoDetalhesDto(
        this ReceitaManipulada receita,
        CalculoReceitaManipulada calculo)
    {
        var rastreioCapsula = calculo.Rastreios
            .FirstOrDefault(x => x.Produto.ClasseProdutoId == TipoClasseProdutoAbreviacao.TipoCapsula);

        var rastreioEmbalagem = calculo.Rastreios
            .FirstOrDefault(x => x.Produto.ClasseProdutoId == TipoClasseProdutoAbreviacao.Embalagem &&
                                 x.TipoOrigem == TipoOrigem.Embalagem);

        var detalhesValores = new ReceitaManipuladaDetalhesResultadoDto.DetalhesValoresDto
        {
            PrecoBruto = calculo.Valores.ValorBruto,
            Desconto = calculo.Valores.ValorDesconto,
            PrecoReceita = calculo.Valores.ValorBruto,
            CustoOperacional = calculo.Valores.CustoOperacional,
            PrecoCustoTotal = calculo.Calculos?.Sum(c => c.ValorCusto) ?? 0,
            PrecoVendaTotal = calculo.Valores.ValorTotal,
            Lucro = calculo.Valores.ValorLucro,
            PercentualLucroTotal = calculo.Calculos?.Sum(c => c.ValorCusto > 0
                ? (c.ValorVenda - c.ValorCusto) / c.ValorCusto * 100
                : 0) ?? 0
        };

        var detalhesVolume = new ReceitaManipuladaDetalhesResultadoDto.DetalhesVolumeDto
        {
            TipoCalculoReceita = receita.FormaFarmaceutica?.TipoCalculo,
            VolumeMateriasPrimas = calculo.BaseCalculo.VolumeMateriaPrima,
            VolumeExcipiente = calculo.BaseCalculo.VolumeExcipienteMinimo,
            PercentualMinimoExcipiente = receita.FormaFarmaceutica?.PercentualMinimoExcipiente,
            VolumeTotalMinimo = calculo.BaseCalculo.VolumeTotal,
            DivisaoDose = calculo.BaseCalculo.QuantidadeDivisaoDose,
            DescricaoCapsula = rastreioCapsula?.Produto?.Descricao,
            NumeroCapsulaId = rastreioCapsula?.Produto?.ProdutoTipoCapsula?.NumeroCapsulaId,
            VolumeCapsula = rastreioCapsula?.Produto?.ProdutoTipoCapsula?.CapsulaTamanho?.VolumeMl,
            DescricaoEmbalagem = rastreioEmbalagem?.Produto?.Descricao,
            VolumeEmbalagem = rastreioEmbalagem?.Produto?.ProdutoEmbalagem?.Volume
        };

        var infoAdicional = new ReceitaManipuladaDetalhesResultadoDto.ReceitaManipuladaInformacoesAdicionaisDto
        {
            DataPrescricao = receita.DataPrescricao,
            DataValidadePrescricao = receita.DataValidadePrescricao,
            DataEmissao = receita.DataEmissao,
            DataValidadeReceita = calculo.BaseCalculo.DataValidade
        };

        var itensPrescritos = receita.Itens
            .OrderBy(i => i.Ordem)
            .Select(i =>
            {
                var descricaoFinal =
                    !string.IsNullOrWhiteSpace(i.DescricaoProduto) &&
                    i.DescricaoProduto != i.Produto?.Descricao &&
                    i.DescricaoProduto != (i as ReceitaManipuladaItemSinonimo)?.ProdutoSinonimo?.Sinonimo
                        ? i.DescricaoProduto
                        : (i as ReceitaManipuladaItemSinonimo)?.ProdutoSinonimo?.Sinonimo
                          ?? i.Produto?.Descricao;

                return new ReceitaManipuladaDetalhesResultadoDto.ReceitaManipuladaItemPrescritoDto
                {
                    DescricaoProduto = descricaoFinal?.Trim(),
                    ProdutoId = i.Produto.Id,
                    Quantidade = i.Quantidade,
                    UnidadeMedidaId = i.UnidadeMedidaId,
                    UnidadeMedidaAbreviacao = ((UnidadeMedidaAbreviacao)i.UnidadeMedidaId).ToString(),
                    TipoQuantificacao = i.Tipo
                };
            }).ToList();

        var itensCalculados = calculo.Calculos
            .Select(MapearItemCalculado)
            .ToList();

        var componentesAninhados = AninharComponentesDesmembrados(itensCalculados);

        return new ReceitaManipuladaDetalhesResultadoDto
        {
            DetalhesReceita = new ReceitaManipuladaDetalhesResultadoDto.DetalhesReceitaDto
            {
                Id = receita.Id,
                
                PacienteDescricao = receita.Paciente?.Nome,
                PacienteId = receita.Paciente?.Id ?? Guid.Empty,
                PrescritorDescricao = receita.Prescritor?.NomeCompleto,
                PrescritorId = receita.Prescritor?.Id,
                
                FormaFarmaceuticaDescricao = receita.FormaFarmaceutica?.Descricao,
                FormaFarmaceuticaApresentacao = receita.FormaFarmaceutica?.Apresentacao,
                FormaFarmaceuticaId = receita.FormaFarmaceutica?.Id ?? Guid.Empty,
                CapsulaDescricao = rastreioCapsula?.Produto?.Descricao,
                CapsulaId = rastreioCapsula?.Produto?.Id,
                EmbalagemDescricao = rastreioEmbalagem?.Produto?.Descricao,
                EmbalagemId = rastreioEmbalagem?.Produto?.Id,
                
                PosologiaDescricao = receita.Posologia?.Descricao,
                PosologiaId = receita.Posologia?.Id,
                
                QuantidadeReceita = receita.QuantidadeReceita,
                QuantidadeRepetir = receita.QuantidadeRepetir,
                QuantidadeDose = receita.QuantidadeDose,
                
                UsoContinuo = receita.UsoContinuo,
                TipoUsoContinuo = receita.TipoUsoContinuo,
                QuantidadeReSell = receita.QuantidadeReSell,
                PeriodicidadeReSell = receita.PeriodicidadeReSell,
                DuracaoPrevistaReSellDias = receita.DuracaoPrevistaReSellDias,
                PrevisaoTerminoReSell = receita.PrevisaoTerminoReSell,
                
                Observacao = receita.Observacao,
                PrevisaoEntrega = receita.PrevisaoEntrega,
                Componentes = itensPrescritos,
                InformacoesAdicionais = infoAdicional
            },
            DetalhesCalculo = new ReceitaManipuladaDetalhesResultadoDto.DetalhesCalculoDto
            {
                FormaFarmaceuticaDescricao = receita.FormaFarmaceutica?.Descricao,
                FormaFarmaceuticaApresentacao = receita.FormaFarmaceutica?.Apresentacao,
                FormaFarmaceuticaId = receita.FormaFarmaceutica?.Id ?? Guid.Empty,
                CapsulaDescricao = rastreioCapsula?.Produto?.Descricao,
                QuantidadeCapsula = rastreioCapsula?.Quantidade ?? 0,
                EmbalagemDescricao = rastreioEmbalagem?.Produto?.Descricao,
                QuantidadeEmbalagem = rastreioEmbalagem?.Quantidade ?? 0,
                VolumeCalculado = calculo.BaseCalculo.VolumeTotal,
                QuantidadeDose = receita.QuantidadeDose,
                ComponentesCalculados = componentesAninhados,
                DetalhesValores = detalhesValores,
                DetalhesVolume = detalhesVolume
            }
        };
    }

    private static ReceitaManipuladaDetalhesResultadoDto.ReceitaManipuladaItemCalculadoDto MapearItemCalculado(
        ReceitaManipuladaCalculo calculo)
    {
        var rastreio = calculo.ReceitaManipuladaRastreioCalculo;
        var produto = rastreio?.Produto;
        var item = rastreio?.ReceitaManipuladaItem;
        var tipoCapsula = produto?.ProdutoTipoCapsula;
        var embalagem = produto?.ProdutoEmbalagem;
        var materiaPrima = produto?.ProdutoMateriaPrima;
        var associado = rastreio?.Associados;
        var sinonimo = (item as ReceitaManipuladaItemSinonimo)?.ProdutoSinonimo;
        var isFilhoFormula = rastreio?.TipoOrigem == TipoOrigem.Desmembrado;

        return new ReceitaManipuladaDetalhesResultadoDto.ReceitaManipuladaItemCalculadoDto
        {
            DescricaoProduto = produto?.Descricao,
            ProdutoId = produto?.Id ?? Guid.Empty,

            QuantidadePrescrita = rastreio?.Quantidade ?? 0,
            UnidadeMedidaPrescritaId = rastreio?.UnidadeMedidaId ?? 0,
            UnidadeMedidaPrescrita = ((UnidadeMedidaAbreviacao?)rastreio?.UnidadeMedidaId)?.ToString(),

            QuantidadeCalculada = calculo.QuantidadeCalculada,
            UnidadeMedidaCalculadaId = calculo.UnidadeMedidaCalculadaId,
            UnidadeMedidaCalculada = ((UnidadeMedidaAbreviacao?)calculo.UnidadeMedidaCalculadaId)?.ToString(),

            Volume = calculo.QuantidadeVolume,
            Fator = calculo.FatorTotal,
            Densidade = materiaPrima?.Densidade ?? 0,

            IsExcipiente = materiaPrima?.IsExcipiente ?? false,
            IsQsp = materiaPrima?.IsQsp ?? false,
            IsAssociado = rastreio?.TipoOrigem == TipoOrigem.Associado,
            IsEmbalagem = rastreio?.TipoOrigem == TipoOrigem.Embalagem,
            IsCapsula = produto?.ClasseProdutoId == TipoClasseProdutoAbreviacao.TipoCapsula,

            EhSinonimo = rastreio?.EhSinonimo ?? false,
            EhEditadoNoRotulo = !isFilhoFormula && rastreio?.EhEditadoNoRotulo == true,

            TipoQuantificacao = rastreio?.TipoComponente,
            TipoOrigem = rastreio?.TipoOrigem,
            TipoDesmembramento = rastreio?.TipoDesmembramento,
            TipoComponenteReceita = rastreio?.EhFormula == true
                ? TipoComponenteReceita.Formula
                : (TipoComponenteReceita?)produto?.ClasseProdutoId,
            DesmembraFormula = rastreio?.FormulaDesmembra ?? false,

            AssociadoAcumula = associado?.Acumula,
            AssociadoDosagemMaxima = associado?.DosagemMaxima,
            AssociadoDosagemMinima = associado?.DosagemMinima,
            AssociadoUnidadeMedidaDosagemId = associado?.UnidadeMedidaDosagemId,

            ProdutoDescricaoOriginal = isFilhoFormula ? null : produto?.Descricao,
            ProdutoDescricaoEditada = isFilhoFormula ? null : item?.DescricaoProduto,
            ProdutoSinonimoDescricao = isFilhoFormula ? null : sinonimo?.Sinonimo,
            ProdutoOrigemId = rastreio?.ProdutoOrigemId,
            ProdutoSinonimoId = rastreio?.ProdutoSinonimoId,

            VolumeMaximoCapsula = tipoCapsula?.CapsulaTamanho?.VolumeMl,
            TamanhoCapsulaId = tipoCapsula?.NumeroCapsulaId,
            CapacidadeMaximaEmbalagem = embalagem?.Volume ?? 0,

            PrecoCusto = calculo.ValorCusto,
            PrecoVenda = calculo.ValorVenda,
            PercentualLucro = calculo.ValorCusto > 0
                ? (calculo.ValorVenda - calculo.ValorCusto) / calculo.ValorCusto * 100
                : 0,

            Fatores = new ReceitaManipuladaDetalhesResultadoDto.FatoresDto
            {
                Equivalencia = calculo.FatorEquivalencia,
                DiluicaoFornecedor = calculo.FatorDiluicaoFornecedor,
                ConcentracaoAgua = calculo.FatorConcentracaoAgua,
                DiluicaoInterna = calculo.FatorDiluicaoInterna,
                Sinonimo = calculo.FatorSinonimo,
                FatorCorrecao = calculo.FatorCorrecao,
                FatorTotal = calculo.FatorTotal
            }
        };
    }

    private static List<ReceitaManipuladaDetalhesResultadoDto.ReceitaManipuladaItemCalculadoDto>
        AninharComponentesDesmembrados(
            List<ReceitaManipuladaDetalhesResultadoDto.ReceitaManipuladaItemCalculadoDto> componentes)
    {
        if (componentes is null || componentes.Count is 0)
            return [];

        var formulas = componentes
            .Where(c =>
                c.TipoComponenteReceita is TipoComponenteReceita.Formula &&
                c.TipoDesmembramento is FormulaPadraoDesmembramento.DesmembraMesmaFicha
                    or FormulaPadraoDesmembramento.DesmembraSeparaFicha)
            .ToList();

        formulas.AddRange(
            componentes
                .Where(c =>
                    c.TipoComponenteReceita is TipoComponenteReceita.Formula &&
                    c.DesmembraFormula && formulas.All(f => f.ProdutoId != c.ProdutoId))
        );

        foreach (var formula in formulas)
        {
            var filhos = componentes
                .Where(f =>
                    f.TipoOrigem is TipoOrigem.Desmembrado &&
                    f.ProdutoOrigemId.HasValue &&
                    f.ProdutoOrigemId.Value == formula.ProdutoId)
                .ToList();

            formula.ComponentesFormula = filhos;
        }

        var idsDosFilhos = formulas
            .SelectMany(f =>
                f.ComponentesFormula ??
                Enumerable.Empty<ReceitaManipuladaDetalhesResultadoDto.ReceitaManipuladaItemCalculadoDto>())
            .Select(f => f.ProdutoId)
            .Where(id => id != Guid.Empty)
            .ToHashSet();

        return componentes
            .Where(c => !idsDosFilhos.Contains(c.ProdutoId))
            .ToList();
    }
}
