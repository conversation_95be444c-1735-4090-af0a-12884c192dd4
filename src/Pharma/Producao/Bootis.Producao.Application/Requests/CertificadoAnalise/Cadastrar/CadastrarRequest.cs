using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Producao.Application.Requests.CertificadoAnalise.Cadastrar;

public class CadastrarRequest
{
    public Guid LoteId { get; set; }
    public decimal QuantidadeAmostra { get; set; }
    public UnidadeMedidaAbreviacao UnidadeAmostragemId { get; set; }
    public IEnumerable<ControleQualidade> ControleQualidade { get; set; }
    public string InformacaoesComplementares { get; set; }
    public bool Aprovado { get; set; }
}

public class ControleQualidade
{
    public Guid EnsaioId { get; set; }
    public string Especificacoes { get; set; }
    public string Resultado { get; set; }
}