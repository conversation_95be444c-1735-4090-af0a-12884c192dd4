using Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;
using MediatR;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Cadastrar;

public class CadastrarReceitaManipuladaResponse : IRequest<ReceitaManipuladaDetalhesResultadoDto>
{
    public ReceitaManipuladaDetalhesResultadoDto Receita { get; set; }
    public List<ObterUpsellResponse> Upsell { get; set; }
}