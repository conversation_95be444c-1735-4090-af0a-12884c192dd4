using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Newtonsoft.Json;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;

public class ObterComponentesResponse
{
    public Guid ComponenteId { get; set; }
    public string DescricaoProduto { get; set; }
    public string DescricaoProdutoOriginal { get; set; }
    public string DescricaoRotulo { get; set; }
    public Guid ProdutoId { get; set; }
    public decimal Quantidade { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public string UnidadeMedidaAbreviacao { get; set; }
    public int ClasseProduto { get; set; }
    public int? Ordem { get; set; }
    public bool OcultaRotulo { get; set; }
    public Guid? SinonimoId { get; set; }
    public TipoComponente TipoQuantificacao { get; set; }

    public Guid? FormulaPadraoId { get; set; }
    
    [JsonIgnore]
    public int TipoComponenteId { get; set; }
    public TipoComponenteReceita TipoComponente => (TipoComponenteReceita)TipoComponenteId;

    public List<ObterComponentesResponse> SubComponentes { get; set; } = new();
}