using Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Validar;
using Bootis.Shared.Common.Interfaces;
using MediatR;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class ValidarIncompatibilidadesRequestHandler(IProdutoIncompativelRepository produtoIncompativelRepository)
    : IRequestHandler<ValidarIncompatibilidadesRequest, IncompatibilidadeResponse?>, IScopedService
{
    public async Task<IncompatibilidadeResponse?> Handle(ValidarIncompatibilidadesRequest request, CancellationToken cancellationToken)
    {
        var produtoIdsList = request.ProdutoIds.ToList();

        if (produtoIdsList.Count < 2)
            return new IncompatibilidadeResponse { Status = IncompatibilidadeStatus.Ok };

        // Uma única consulta ao banco para buscar todas as incompatibilidades
        var todasIncompatibilidades = await produtoIncompativelRepository
            .ObterTodasIncompatibilidadesEntreAsync(produtoIdsList);

        var incompatibilidadesList = DeduplicarPares(todasIncompatibilidades.ToList());

        if (incompatibilidadesList.Count == 0)
            return new IncompatibilidadeResponse { Status = IncompatibilidadeStatus.Ok };

        // Separar por nível de incompatibilidade
        var bloqueios = incompatibilidadesList.Where(i => i.NivelIncompatibilidade == NivelIncompatibilidade.Bloqueio).ToList();
        var avisos = incompatibilidadesList.Where(i => i.NivelIncompatibilidade == NivelIncompatibilidade.ApenasAviso).ToList();

        // Converter para DTOs
        var incompatibilidadesDto = incompatibilidadesList.Select(i => new ParIncompativelDto
        {
            ProdutoId = i.ProdutoId,
            ProdutoDescricao = i.Produto?.Descricao ?? "Produto não encontrado",
            ProdutoIncompativelId = i.ProdutoIncompativelId,
            ProdutoIncompativelDescricao = i.ProdutoIncompatibilidade?.Descricao ?? "Produto não encontrado",
            Nivel = i.NivelIncompatibilidade,
            Descricao = i.Descricao
        }).ToList();

        // Determinar status e tipo baseado na presença de bloqueios
        var status = bloqueios.Count > 0 ? IncompatibilidadeStatus.Bloqueio : IncompatibilidadeStatus.Aviso;

        // O tipo é determinado pela quantidade de incompatibilidades do nível que define o status
        var incompatibilidadesQueDefinem = status == IncompatibilidadeStatus.Bloqueio ? bloqueios : avisos;
        var tipo = incompatibilidadesQueDefinem.Count == 1 ? IncompatibilidadeTipo.Unica : IncompatibilidadeTipo.Multipla;

        return new IncompatibilidadeResponse
        {
            Status = status,
            Tipo = tipo,
            TotalBloqueios = bloqueios.Count,
            TotalAvisos = avisos.Count,
            Incompatibilidades = incompatibilidadesDto
        };
    }

    private static List<ProdutoIncompativel> DeduplicarPares(List<ProdutoIncompativel> incompatibilidades)
    {
        var paresDeduplicated = new Dictionary<string, ProdutoIncompativel>();

        foreach (var incomp in incompatibilidades)
        {
            var menorId = incomp.ProdutoId < incomp.ProdutoIncompativelId ? incomp.ProdutoId : incomp.ProdutoIncompativelId;
            var maiorId = incomp.ProdutoId > incomp.ProdutoIncompativelId ? incomp.ProdutoId : incomp.ProdutoIncompativelId;
            var chave = $"{menorId}-{maiorId}";

            paresDeduplicated.TryAdd(chave, incomp);
        }

        return paresDeduplicated.Values.ToList();
    }
}