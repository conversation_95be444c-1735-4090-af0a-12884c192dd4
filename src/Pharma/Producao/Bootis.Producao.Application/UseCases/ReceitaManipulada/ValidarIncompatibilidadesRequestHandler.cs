using Bootis.Producao.Application.Requests.ReceitaManipulada.Validar;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;
using Bootis.Producao.Domain.Services.ReceitaManipulada;
using Bootis.Shared.Common.Interfaces;
using MediatR;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class ValidarIncompatibilidadesRequestHandler : IRequestHandler<ValidarIncompatibilidadesRequest, IncompatibilidadeResponseDto?>, IScopedService
{
    private readonly IValidadorIncompatibilidadeService _validadorIncompatibilidade;

    public ValidarIncompatibilidadesRequestHandler(IValidadorIncompatibilidadeService validadorIncompatibilidade)
    {
        _validadorIncompatibilidade = validadorIncompatibilidade;
    }

    public async Task<IncompatibilidadeResponseDto?> Handle(ValidarIncompatibilidadesRequest request, CancellationToken cancellationToken)
    {
        return await _validadorIncompatibilidade.ObterCenariosIncompatibilidadeAsync(request.ProdutoIds);
    }
}
