<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{c13fe385-cc8d-4727-8ae2-dc243ae13958}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.8" />
        <PackageReference Include="MediatR" Version="13.0.0"/>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Estoque\Bootis.Estoque.Application\Bootis.Estoque.Application.csproj"/>
        <ProjectReference Include="..\..\..\Shared\Bootis.Shared.Application\Bootis.Shared.Application.csproj"/>
        <ProjectReference Include="..\Bootis.Producao.Common\Bootis.Producao.Common.csproj"/>
        <ProjectReference Include="..\Bootis.Producao.Domain\Bootis.Producao.Domain.csproj"/>
    </ItemGroup>

</Project>

