using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.ProdutoLoteEmUsoAggregate;
using Bootis.Estoque.Domain.Dtos.Lote;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Domain.Services;

public class LoteSeletorService(ILoteRepository loteRepository) : ILoteSeletorService, IScopedService
{
    public async Task<LoteSelecaoResultado> ResolverConsumoDeLotesAsync(
        Produto produto,
        Guid formaFarmaceuticaId,
        UnidadeMedidaAbreviacao unidadeEntrada,
        decimal quantidadeNecessaria)
    {
        var quantidadeRestante = quantidadeNecessaria;
        var lotes = new List<LoteIdComSaldoDto>();

        var loteEmUso = await loteRepository.ObterLoteEmUsoComSaldoAsync(produto.Id);
        if (loteEmUso != null)
            lotes.Add(loteEmUso);

        var lotesExtras = await loteRepository.ObterLotesDisponiveisPorFormaFarmaceuticaAsync(
            formaFarmaceuticaId, produto.Id
        );

        lotes.AddRange(lotesExtras.Where(l => l.LoteId != loteEmUso?.LoteId));

        var lotesConsumidos = new List<LoteSelecionado>();
        ProdutoLoteEmUso loteEmUsoAtualizado = null;

        foreach (var lote in lotes)
        {
            if (quantidadeRestante <= 0)
                break;

            var saldoConvertido = ConverterUnidade(lote.Saldo, lote.UnidadeMedidaId, unidadeEntrada);
            if (saldoConvertido <= 0)
                continue;

            var quantidadeConsumir = Math.Min(saldoConvertido, quantidadeRestante);
            var quantidadeEstoque = ConverterUnidade(quantidadeConsumir, unidadeEntrada, lote.UnidadeMedidaId);

            lotesConsumidos.Add(new LoteSelecionado(
                lote.LoteId,
                quantidadeConsumir,
                unidadeEntrada,
                quantidadeEstoque,
                lote
            ));

            quantidadeRestante -= quantidadeConsumir;
            loteEmUsoAtualizado = new ProdutoLoteEmUso(produto.Id, lote.LoteId, lote.LocalEstoqueId);
        }

        if (lotesConsumidos.Any())
        {
            var quantidadeConsumida = lotesConsumidos.Sum(l => l.QuantidadeConsumida);
            var diferenca = quantidadeNecessaria - quantidadeConsumida;

            if (diferenca > 0.0001m)
                throw new DomainException(
                    $"Não há saldo suficiente para o produto '{produto.Descricao}' na forma farmacêutica informada. " +
                    $"Necessário: {quantidadeNecessaria:N2} {unidadeEntrada}, disponível: {quantidadeConsumida:N2}."
                );
        }

        return new LoteSelecaoResultado
        {
            LotesConsumidos = lotesConsumidos,
            LoteEmUsoAtualizado = loteEmUsoAtualizado
        };
    }

    private static decimal ConverterUnidade(decimal quantidade, object unidadeOrigem, object unidadeDestino)
    {
        var origem = unidadeOrigem switch
        {
            int id => (UnidadeMedidaAbreviacao)id,
            UnidadeMedidaAbreviacao abrev => abrev,
            _ => throw new ArgumentException("Tipo inválido para unidade de origem")
        };

        var destino = unidadeDestino switch
        {
            int id => (UnidadeMedidaAbreviacao)id,
            UnidadeMedidaAbreviacao abrev => abrev,
            _ => throw new ArgumentException("Tipo inválido para unidade de destino")
        };

        var conversao = ConversaoUnidadeMedidaCreator.Criar(origem, destino);
        return conversao.CalcularConversao(quantidade);
    }

    public record LoteSelecionado(
        Guid LoteId,
        decimal QuantidadeConsumida,
        UnidadeMedidaAbreviacao UnidadeEntrada,
        decimal QuantidadeConvertidaEstoque,
        LoteIdComSaldoDto LoteInfo);

    public record LoteSelecaoResultado(
        List<LoteSelecionado> LotesConsumidos,
        ProdutoLoteEmUso LoteEmUsoAtualizado)
    {
        public LoteSelecaoResultado() : this(new List<LoteSelecionado>(), null)
        {
        }
    }
}