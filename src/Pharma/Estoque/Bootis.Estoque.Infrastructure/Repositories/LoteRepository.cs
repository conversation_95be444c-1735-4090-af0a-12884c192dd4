using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.Dtos.Lote;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Estoque.Infrastructure.Repositories;

public class LoteRepository(IDbContext context) : Repository<Lote>(context), ILoteRepository
{
    public Task<Lote> ObterLotePorNumeroAsync(string numero, Guid produtoId, int numeroNf, int serieNf)
    {
        return DbSet
            .SingleOrDefaultAsync(c =>
                c.Numero == numero && c.ProdutoId == produtoId && c.NumeroNf == numeroNf && c.SerieNf == serieNf);
    }

    public Task<Lote> ObterLotePorIdAsync(Guid id)
    {
        return DbSet
            .Include(c => c.Produto)
            .Include(c => c.LoteInformacaoTecnica)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public Task<List<Lote>> ObterLotesPorIdAsync(IEnumerable<Guid> ids)
    {
        return Context.Set<Lote>()
            .Include(c => c.LoteInformacaoTecnica)
            .Include(c => c.Produto)
            .Where(c => ids.Contains(c.Id))
            .ToListAsync();
    }

    public async Task<IEnumerable<(Guid LoteId, string Numero, string ProdutoDescricao)>>
        VerificarDependenciasLotesAsync(IEnumerable<Guid> ids)
    {
        const string sql = """
                            SELECT l.id,
                                  l.numero,
                                  pr.descricao AS produto_descricao
                           FROM lotes l
                                LEFT JOIN saldos_estoque s ON s.lote_id = l.id
                                LEFT JOIN lotes_informacao_tecnica lo ON lo.lote_origem_id = l.id
                                LEFT JOIN perdas p ON p.lote_id = l.id
                                LEFT JOIN ajuste_saldo_estoque a ON a.lote_id = l.id
                                LEFT JOIN produto_lotes_em_uso pl ON pl.lote_id = l.id
                                LEFT JOIN movimentos_estoque me ON me.lote_id = l.id
                                LEFT JOIN transferencias_lote_itens tli ON tli.lote_id = l.id
                                LEFT JOIN produtos pr ON pr.id = l.produto_id
                           WHERE l.id = ANY ( @ids )
                           GROUP BY l.numero,
                                    l.id,
                                    pr.descricao
                           HAVING COUNT(s.lote_id) +
                                  COUNT(lo.lote_origem_id) +
                                  COUNT(p.lote_id) +
                                  COUNT(pl.lote_id) +
                                  COUNT(a.lote_id) +
                                  COUNT(me.lote_id) +
                                  COUNT(tli.lote_id) > 0;
                           """;

        var dbConnection = Context.Database.GetDbConnection();
        var queryResult = await dbConnection.QueryAsync<(Guid LoteId, string Numero, string ProdutoDescricao)>(
            sql, new { ids }, Context.Database.CurrentTransaction?.GetDbTransaction());

        return queryResult;
    }

    public Task<decimal?> ObterDensidadeLoteAsync(Guid id)
    {
        const string sql = """
                           SELECT lo.densidade
                           FROM lotes l
                                LEFT JOIN lotes_informacao_tecnica lo ON lo.id = l.id
                           WHERE l.id = @id;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return dbConnection.QueryFirstOrDefaultAsync<decimal?>(sql, new { id },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<IEnumerable<Guid>> ObterLoteUnidadeAlternativaIdPorProdutoIdAsync(Guid produtoId)
    {
        const string sql = """
                           SELECT lua.unidade_alternativa_id
                           FROM lotes_unidade_alternativa lua
                                JOIN lotes_informacao_tecnica lit ON lit.id = lua.lote_informacao_tecnica_id
                                JOIN lotes lot ON lot.id = lit.id
                                JOIN produtos p ON p.id = lot.produto_id
                           WHERE p.id = @produtoId;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QueryAsync<Guid>(sql, new { produtoId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<List<LoteIdComSaldoDto>> ObterLotesDisponiveisPorFormaFarmaceuticaAsync(Guid formaFarmaceuticaId,
        Guid produtoId)
    {
        const string sql = """
                               SELECT l.id AS lote_id, 
                                      se.saldo, 
                                      se.unidade_medida_id,
                                      lit.fator_diluicao_fornecedor,
                                      lit.fator_diluicao_interna,
                                      lit.fator_concentracao_agua,
                                      lit.densidade,
                                      lua.unidade_alternativa_id,
                                      le.id AS local_estoque_id
                                 FROM saldos_estoque se
                                      JOIN locais_estoque le ON le.id = se.local_estoque_id 
                                      JOIN laboratorios lab ON lab.local_estoque_id = le.id
                                      JOIN formas_farmaceutica ff ON ff.laboratorio_id = lab.id
                                      JOIN lotes l ON l.id = se.lote_id
                                      JOIN lotes_informacao_tecnica lit ON lit.lote_id = l.id
                                      LEFT JOIN lotes_unidade_alternativa lua ON lua.lote_informacao_tecnica_id = lit.lote_id
                                WHERE ff.id = @formaFarmaceuticaId
                                  AND se.produto_id = @produtoId
                                  AND se.bloqueado = false
                                  AND se.saldo > 0
                                  AND l.data_validade > @dataAtual
                                  AND l.situacao != @loteBloqueado
                                ORDER BY l.data_validade,
                                         se.saldo DESC;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        var parametros = new
        {
            formaFarmaceuticaId,
            produtoId,
            dataAtual = DateTime.UtcNow.Date,
            loteBloqueado = (int)SituacaoLote.Bloqueado
        };

        var resultado = await dbConnection.QueryAsync<LoteIdComSaldoDto>(
            sql,
            parametros,
            Context.Database.CurrentTransaction?.GetDbTransaction()
        );

        return resultado.ToList();
    }

    public async Task<LoteIdComSaldoDto> ObterLoteEmUsoComSaldoAsync(Guid produtoId)
    {
        const string sql = """
                               SELECT l.id AS lote_id, 
                                      se.saldo, 
                                      se.unidade_medida_id,
                                      lit.fator_diluicao_fornecedor,
                                      lit.fator_diluicao_interna,
                                      lit.fator_concentracao_agua,
                                      lit.densidade,
                                      se.local_estoque_id
                                 FROM produto_lotes_em_uso plu
                                      JOIN lotes l ON l.id = plu.lote_id
                                      JOIN saldos_estoque se ON se.lote_id = l.id
                                      JOIN locais_estoque le ON le.id = se.local_estoque_id 
                                      JOIN lotes_informacao_tecnica lit ON lit.lote_id = l.id
                                WHERE plu.produto_id = @ProdutoId
                                  AND se.bloqueado = false
                                  AND se.saldo > 0
                                  AND l.data_validade > @DataAtual
                                  AND l.situacao != @LoteBloqueado
                                ORDER BY l.data_validade,
                                         se.saldo DESC
                                LIMIT 1
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        var result = await dbConnection.QueryFirstOrDefaultAsync<LoteIdComSaldoDto>(
            sql,
            new
            {
                ProdutoId = produtoId,
                DataAtual = DateTime.UtcNow.Date,
                LoteBloqueado = SituacaoLote.Bloqueado
            },
            Context.Database.CurrentTransaction?.GetDbTransaction());

        return result;
    }

    public void Add(LoteInformacaoTecnica informacaoTecnica)
    {
        Context.Set<LoteInformacaoTecnica>()
            .Add(informacaoTecnica);
    }

    public void Update(LoteInformacaoTecnica informacaoTecnica)
    {
        Context.Set<LoteInformacaoTecnica>()
            .Update(informacaoTecnica);
    }

    public void Remove(LoteInformacaoTecnica informacaoTecnica)
    {
        Context.Set<LoteInformacaoTecnica>()
            .Remove(informacaoTecnica);
    }
}