using System.Runtime.CompilerServices;
using Bootis.Report.Web.Models;
using Bootis.Report.Web.ReportSources;
using Bootis.Report.Web.Services;
using Bootis.Shared;
using Bootis.Shared.Application.Interfaces;
using DevExpress.AspNetCore.Reporting.WebDocumentViewer;
using DevExpress.XtraReports.Web.Extensions;
using DevExpress.XtraReports.Web.ReportDesigner.Services;
using DevExpress.XtraReports.Web.WebDocumentViewer;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;

namespace Bootis.Report.Web.Controllers;

[IgnoreAntiforgeryToken]
[Authorize]
public class HomeController(
    IUserContext userContext,
    IReportParameterValidator parameterValidator,
    IReportLoader reportLoader,
    ReportDataSourceProvider dataSourceProvider,
    ILogger<HomeController> logger) : Controller
{
    public IActionResult Index()
    {
        return View();
    }

    public IActionResult Error()
    {
        return View(new ErrorModel());
    }

    [Route("view/{reportType}/{id}")]
    public async Task<IActionResult> Viewer(
        [FromRoute] string reportType,
        [FromRoute] string id,
        [FromServices] IWebDocumentViewerClientSideModelGenerator webDocumentViewerClientSideModelGenerator,
        [FromServices] Func<string, IReportSource> reportFactory)
    {
        var reportUrl = BuildReportUrl(reportType, id);
        var reportSource = reportFactory(reportType);

        if (reportSource is null)
            return NotFound();

        var report = reportSource.GetReport();
        var viewerModel =
            await webDocumentViewerClientSideModelGenerator.GetModelAsync(report,
                WebDocumentViewerController.DefaultUri);

        var model = CreateCustomViewerModel(viewerModel, reportUrl, reportType);
        return View(model);
    }

    [HttpGet]
    [Route("view/{reportType}/{id}")]
    public async Task<IActionResult> ViewReport(
        [FromRoute] string reportType,
        [FromRoute] string id,
        [FromQuery] ViewReportRequest request,
        [FromServices] IWebDocumentViewerClientSideModelGenerator webDocumentViewerClientSideModelGenerator,
        [FromServices] ReportStorageWebExtension reportStorage,
        [FromServices] IValidator<ViewReportRequest> validator,
        [FromServices] IHttpContextAccessor httpContextAccessor)
    {
        var validationTask = validator.ValidateAsync(request);
        var reportUrl = BuildReportUrl(reportType, id);

        var validationResult = await validationTask;
        if (!validationResult.IsValid)
            return CreateValidationErrorResponse(validationResult);

        try
        {
            var report = await reportLoader.LoadReportAsync(reportStorage, reportUrl);

            if (report is null)
                return NotFound(new { message = $"Report type '{reportType}' not found" });

            var parameters = ExtractParametersFromQuery(httpContextAccessor.HttpContext);

            var parameterValidation = parameterValidator.ValidateAndApplyParameters(report, parameters);
            if (!parameterValidation.IsValid)
                return CreateParameterErrorResponse(parameterValidation.Errors);

            if (request.HideParametersPanel)
                report.RequestParameters = false;

            var viewerModel =
                await webDocumentViewerClientSideModelGenerator.GetModelAsync(report,
                    WebDocumentViewerController.DefaultUri);
            var model = CreateCustomViewerModel(viewerModel, reportUrl, reportType, request.HideParametersPanel,
                request.Format);

            return View("ViewReport", model);
        }
        catch (UnauthorizedAccessException)
        {
            return Forbid();
        }
        catch (Exception ex)
        {
            return StatusCode(500,
                new { message = "Internal server error while generating report", details = ex.Message });
        }
    }

    [HttpGet]
    [Route("edit/{reportType}/{id}")]
    public async Task<IActionResult> Edit(
        [FromRoute] string reportType,
        [FromRoute] string id,
        [FromServices] IReportDesignerModelBuilder reportDesignerModelBuilder,
        [FromServices] Func<string, IReportSource> reportFactory,
        [FromServices] ReportStorageWebExtension reportStorageWeb)
    {
        var reportUrl = BuildReportUrl(reportType, id);

        var reportSource = reportFactory(reportType);
        if (reportSource == null)
            return NotFound();

        var report = reportSource.GetReport();
        var data = await reportStorageWeb.GetDataAsync(reportUrl);
        using var stream = new MemoryStream(data);
        report.LoadLayout(stream);

        var designerModel = await reportDesignerModelBuilder
            .Report(report)
            .DataSources(LoadDataSourceWithLogging)
            .BuildModelAsync();

        designerModel.ReportUrl = reportUrl;

        return View(new CustomReportDesignerModel
        {
            ReportDesignerModel = designerModel,
            ReportType = reportType,
            Theme = userContext.UserSession.UserPreferences.Theme,
            TenantId = userContext.TenantId,
            GroupTenantId = userContext.GroupTenantId,
            Id = id
        });
    }

    [HttpGet]
    [Route("edit/{reportType}")]
    public async Task<IActionResult> Edit(
        [FromRoute] string reportType,
        [FromServices] IReportDesignerModelBuilder reportDesignerModelBuilder,
        [FromServices] Func<string, IReportSource> reportFactory,
        [FromServices] ReportStorageWebExtension reportStorageWeb)
    {
        if (userContext.TenantId != DefaultsValues.BootisId)
            return Unauthorized();

        var reportUrl = $"Defaults/{reportType}";

        var reportSource = reportFactory(reportType);
        if (reportSource == null)
            return NotFound();

        var report = reportSource.GetReport();
        var data = await reportStorageWeb.GetDataAsync(reportUrl);
        using var stream = new MemoryStream(data);
        report.LoadLayout(stream);

        var designerModel = await reportDesignerModelBuilder
            .Report(report)
            .DataSources(LoadDataSourceWithLogging)
            .BuildModelAsync();

        designerModel.ReportUrl = reportUrl;

        return View(new CustomReportDesignerModel
        {
            ReportDesignerModel = designerModel,
            ReportType = reportType,
            Theme = userContext.UserSession.UserPreferences.Theme,
            TenantId = userContext.TenantId,
            GroupTenantId = userContext.GroupTenantId
        });
    }

    private void LoadDataSourceWithLogging(IDictionary<string, object> dataSources)
    {
        foreach (var source in dataSourceProvider.GetAvailableDataSources())
            try
            {
                var dataSource = source.ReportSource.GetDataSource();
                if (dataSource != null) dataSources.Add(source.Name, dataSource);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error loading data source '{SourceName}': {ErrorMessage}", source.Name,
                    ex.Message);
            }
    }

    [HttpPost]
    [Route("print/{reportType}/{id}")]
    public async Task<IActionResult> Print(
        [FromRoute] string reportType,
        [FromRoute] string id,
        [FromBody] ReportRequest request,
        [FromServices] ReportStorageWebExtension reportStorage)
    {
        try
        {
            var reportUrl = BuildReportUrl(reportType, id);

            var report = await reportLoader.LoadReportAsync(reportStorage, reportUrl);
            if (report is null)
                return NotFound("Report not found.");

            var parameters = request.ToStringValues();
            var parameterValidation = parameterValidator.ValidateAndApplyParameters(report, parameters);
            if (!parameterValidation.IsValid)
                return BadRequest($"Erro nos parâmetros: {string.Join(", ", parameterValidation.Errors)}");

            var pdfBytes = await reportLoader.ExportToPdfAsync(report);
            return File(pdfBytes, "application/pdf", $"{reportType}.pdf");
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erro ao gerar o relatório: {ex.Message}");
        }
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private string BuildReportUrl(string reportType, string id)
    {
        return $"{userContext.TenantId}/{reportType}/{id}";
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private CustomViewerModel CreateCustomViewerModel(
        WebDocumentViewerModel viewerModel,
        string reportUrl,
        string reportType,
        bool isParametersReadOnly = false,
        string format = null)
    {
        return new CustomViewerModel
        {
            ViewerModel = viewerModel,
            ReportUrl = reportUrl,
            ReportType = reportType,
            Theme = userContext.UserSession.UserPreferences.Theme,
            IsParametersReadOnly = isParametersReadOnly,
            Format = format
        };
    }


    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private static BadRequestObjectResult CreateValidationErrorResponse(ValidationResult validationResult)
    {
        return new BadRequestObjectResult(new
        {
            message = "Invalid request parameters",
            errors = validationResult.Errors.Select(e => new
            {
                field = e.PropertyName,
                message = e.ErrorMessage
            })
        });
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private static BadRequestObjectResult CreateParameterErrorResponse(List<string> errors)
    {
        return new BadRequestObjectResult(new
        {
            message = "Invalid report parameters", errors
        });
    }


    private static Dictionary<string, StringValues> ExtractParametersFromQuery(HttpContext context)
    {
        var parameters = new Dictionary<string, StringValues>(StringComparer.OrdinalIgnoreCase);

        foreach (var queryParam in context.Request.Query)
        {
            if (queryParam.Key.Equals("HideParametersPanel", StringComparison.OrdinalIgnoreCase) ||
                queryParam.Key.Equals("Format", StringComparison.OrdinalIgnoreCase) ||
                queryParam.Key.Equals("token", StringComparison.OrdinalIgnoreCase))
                continue;

            if (queryParam.Key.StartsWith("parameters[", StringComparison.OrdinalIgnoreCase) &&
                queryParam.Key.EndsWith("]"))
            {
                var paramName = queryParam.Key.Substring(11, queryParam.Key.Length - 12);

                if (parameters.TryGetValue(paramName, out var existingValues))
                {
                    var combinedValues = existingValues.ToList();
                    combinedValues.AddRange(queryParam.Value);
                    parameters[paramName] = new StringValues(combinedValues.ToArray());
                }
                else
                {
                    parameters[paramName] = queryParam.Value;
                }
            }
            else
            {
                parameters[queryParam.Key] = queryParam.Value;
            }
        }

        return parameters;
    }
}