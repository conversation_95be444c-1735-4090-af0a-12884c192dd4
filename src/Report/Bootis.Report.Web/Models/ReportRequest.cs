using System.Text.Json;
using Microsoft.Extensions.Primitives;

namespace Bootis.Report.Web.Models;

public class ReportRequest : Dictionary<string, JsonElement>
{
    public Dictionary<string, StringValues> ToStringValues()
    {
        var result = new Dictionary<string, StringValues>(StringComparer.OrdinalIgnoreCase);

        foreach (var param in this)
        {
            result[param.Key] = ConvertToStringValues(param.Value);
        }

        return result;
    }

    private static StringValues ConvertToStringValues(JsonElement element)
    {
        switch (element.ValueKind)
        {
            case JsonValueKind.Array:
                return new StringValues(element.EnumerateArray().Select(GetStringValue).ToArray());

            case JsonValueKind.String:
                return new StringValues(element.GetString());

            case JsonValueKind.Number:
                return new StringValues(element.GetRawText());

            case JsonValueKind.True:
            case JsonValueKind.False:
                return new StringValues(element.GetBoolean().ToString());

            case JsonValueKind.Null:
                return StringValues.Empty;

            default:
                return new StringValues(element.GetRawText());
        }
    }

    private static string GetStringValue(JsonElement element)
    {
        return element.ValueKind switch
        {
            JsonValueKind.String => element.GetString(),
            JsonValueKind.Number => element.GetRawText(),
            JsonValueKind.True or JsonValueKind.False => element.GetBoolean().ToString(),
            JsonValueKind.Null => null,
            _ => element.GetRawText()
        };
    }
}